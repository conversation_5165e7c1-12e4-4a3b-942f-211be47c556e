package cn.voriya.exporter.thread;

import cn.voriya.boot.thread.DynamicThreadPool;
import cn.voriya.exporter.entity.enums.CollectionTaskType;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

public class ThreadPool {
    public static final DynamicThreadPool TOPIC_NET_POOL = new DynamicThreadPool(CollectionTaskType.TOPIC.getRedisKey(), 1);
    public static final DynamicThreadPool GROUP_NET_POOL = new DynamicThreadPool(CollectionTaskType.GROUP.getRedisKey(), 1);
    public static final DynamicThreadPool BROADCAST_GROUP_NET_POOL = new DynamicThreadPool(CollectionTaskType.BROAD_CAST_GROUP.getRedisKey(), 1);
    public static final DynamicThreadPool CLIENT_NET_POOL = new DynamicThreadPool(CollectionTaskType.CLIENT.getRedisKey(), 1);
    private static final DynamicThreadPool VIEW_REFRESH_POOL = new DynamicThreadPool("view-refresh", 20);
    private static final DynamicThreadPool KAFKA_REFRESH_POOL = new DynamicThreadPool("kafka-refresh", 20);
    public static final ExecutorService BROKER_NET_POOL = Executors.newThreadPerTaskExecutor(
            Thread.ofVirtual().name("broker-net-thread-", 0).factory()
    );

    public static ThreadPoolExecutor getTypePool(CollectionTaskType taskType) {
        return switch (taskType) {
            case TOPIC -> ThreadPool.TOPIC_NET_POOL;
            case BROAD_CAST_GROUP -> ThreadPool.BROADCAST_GROUP_NET_POOL;
            case GROUP -> ThreadPool.GROUP_NET_POOL;
            case CLIENT -> ThreadPool.CLIENT_NET_POOL;
        };
    }

    public static ThreadPoolExecutor getViewRefreshPool() {
        return VIEW_REFRESH_POOL;
    }
    public static ThreadPoolExecutor getKafkaRefreshPool() {
        return KAFKA_REFRESH_POOL;
    }
}
