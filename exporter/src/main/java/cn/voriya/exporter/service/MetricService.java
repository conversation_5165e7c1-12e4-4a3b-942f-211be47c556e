package cn.voriya.exporter.service;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.exporter.cache.RedisQueueOffset;
import cn.voriya.exporter.cache.RedisStorage;
import cn.voriya.exporter.client.TurboClient;
import cn.voriya.exporter.client.ViewBrokerStatsDataFunc;
import cn.voriya.exporter.entity.TaskInfo;
import cn.voriya.exporter.entity.dto.BroadCastConsumeStatsDTO;
import cn.voriya.exporter.entity.dto.ConsumeResult;
import cn.voriya.exporter.entity.dto.GroupBrokerStatsStorage;
import cn.voriya.exporter.entity.dto.TopicResult;
import cn.voriya.exporter.entity.enums.CollectionTaskType;
import cn.voriya.exporter.metrics.broad.BroadCastGroupStatsBrokerMetric;
import cn.voriya.exporter.metrics.broad.client.BroadCastGroupBrokerLagMetric;
import cn.voriya.exporter.metrics.broad.client.BroadCastGroupBrokerOffsetMetric;
import cn.voriya.exporter.metrics.broker.*;
import cn.voriya.exporter.metrics.queue.GroupQueueLagMetric;
import cn.voriya.exporter.metrics.queue.GroupQueueOffsetMetric;
import cn.voriya.exporter.metrics.queue.TopicQueueOffsetMetric;
import cn.voriya.exporter.metrics.rocket.ConsumeNumsTPSMetric;
import cn.voriya.exporter.metrics.rocket.ConsumeSizeTPSMetric;
import cn.voriya.exporter.metrics.rocket.ProduceNumsTPSMetric;
import cn.voriya.exporter.metrics.rocket.ProduceSizeTPSMetric;
import cn.voriya.exporter.metrics.route.GroupRouteMetric;
import cn.voriya.exporter.metrics.route.TopicRouteMetric;
import cn.voriya.exporter.metrics.time.TaskCollectCompleteTimeMetric;
import cn.voriya.exporter.metrics.time.TaskCollectCostTimeMetric;
import cn.voriya.exporter.rocket.Utils;
import com.alibaba.rocketmq.common.Pair;
import com.alibaba.rocketmq.common.admin.OffsetWrapper;
import com.alibaba.rocketmq.common.admin.TopicOffset;
import com.alibaba.rocketmq.common.message.MessageQueue;
import com.alibaba.rocketmq.common.protocol.body.BrokerStatsData;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.alibaba.rocketmq.common.protocol.route.BrokerData;
import com.alibaba.rocketmq.remoting.protocol.RemotingSerializable;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class MetricService {
    private static final String SPLIT = "@";

    public static <T extends RemotingSerializable> List<AbstractMetric> makeTimeCostMetrics(CollectionTaskType taskType,
                                                                                            List<TaskInfo<T>> taskInfoList) {
        List<AbstractMetric> metrics = new LinkedList<>();
        for (TaskInfo<T> taskInfo : taskInfoList) {
            metrics.add(new TaskCollectCompleteTimeMetric(taskType, taskInfo.getTaskId(), taskInfo.getEndTime()));
            metrics.add(new TaskCollectCostTimeMetric(taskType, taskInfo.getTaskId(), taskInfo.getCost()));
        }
        return metrics;
    }

    public static List<AbstractMetric> makeTopicMetrics(List<TaskInfo<TopicResult>> topicInfoList) {
        List<AbstractMetric> metrics = new LinkedList<>();
        List<Pair<String, Map<MessageQueue, TopicOffset>>> currentOffsetTableList = new ArrayList<>();
        for (var info : topicInfoList) {
            String topic = info.getTaskId();
            TopicResult result = info.getResult();
            HashMap<MessageQueue, TopicOffset> currentOffsetTable = result.getOffsetTable();
            currentOffsetTableList.add(new Pair<>(topic, currentOffsetTable));
            //topic的生产流量大小
            Map<String, BrokerStatsData> brokerStatsDatas = result.getBrokerSizeStatsDatas();
            Map<String, BrokerStatsData> numsStatsDatas = result.getBrokerNumsStatsDatas();
            Map<String, Long> brokerOffsetMap = new HashMap<>();
            for (BrokerData brokerData : info.getBrokerDataList()) {
                String brokerName = brokerData.getBrokerName();
                String addr = ViewBrokerStatsDataFunc.getAddrFunc().apply(brokerData);
                metrics.add(new TopicRouteMetric(topic, brokerName));
                BrokerStatsData stats = brokerStatsDatas.get(addr);
                if (Objects.nonNull(stats)) {
                    long sum = stats.getStatsMinute().getSum();
                    double tps = stats.getStatsMinute().getTps();
                    metrics.add(new ProduceSizeMetric(topic, brokerName, sum));
                    metrics.add(new ProduceSizeTPSMetric(topic, brokerName, Utils.getFixedDouble(tps)));
                }
                BrokerStatsData numsTats = numsStatsDatas.get(addr);
                if (Objects.nonNull(numsTats)) {
                    long sum = numsTats.getStatsMinute().getSum();
                    double tps = numsTats.getStatsMinute().getTps();
                    metrics.add(new ProduceNumsMetric(topic, brokerName, sum));
                    metrics.add(new ProduceNumsTPSMetric(topic, brokerName, Utils.getFixedDouble(tps)));
                }
            }
            for (var entry : currentOffsetTable.entrySet()) {
                MessageQueue queue = entry.getKey();
                String brokerName = queue.getBrokerName();
                String brokerIdc = getBrokerIdc(brokerName);
                int queueId = queue.getQueueId();
                long maxOffset = entry.getValue().getMaxOffset();
                brokerOffsetMap.put(brokerName, brokerOffsetMap.getOrDefault(brokerName, 0L) + maxOffset);
                metrics.add(new TopicQueueOffsetMetric(topic, brokerName, brokerIdc, queueId, maxOffset));
            }
            for (var entry : brokerOffsetMap.entrySet()) {
                String brokerName = entry.getKey();
                String brokerIdc = getBrokerIdc(brokerName);
                metrics.add(new TopicBrokerOffsetMetric(topic, brokerName, brokerIdc, entry.getValue()));
            }
        }
        RedisQueueOffset.getTopicInstance().asyncSendAndSet(currentOffsetTableList);
        return metrics;
    }

    public static List<AbstractMetric> makeGroupMetrics(List<TaskInfo<ConsumeResult>> groupInfoList) {
        List<AbstractMetric> metrics = new LinkedList<>();
        List<Pair<String, Map<MessageQueue, OffsetWrapper>>> currentOffsetTableList = new ArrayList<>();
        for (TaskInfo<ConsumeResult> taskInfo : groupInfoList) {
            String group = taskInfo.getTaskId();
            ConsumeResult result = taskInfo.getResult();
            Map<String, Long> brokerConsumerOffsetMap = new HashMap<>();
            Map<String, Long> brokerConsumerDiffMap = new HashMap<>();
            metrics.addAll(makeGroupBrokerStatsMetrics(group, taskInfo, MessageModel.CLUSTERING));
            HashMap<MessageQueue, OffsetWrapper> currentOffsetTable = result.getOffsetTable();
            currentOffsetTableList.add(new Pair<>(group, currentOffsetTable));
            for (var entry : currentOffsetTable.entrySet()) {
                MessageQueue mq = entry.getKey();
                OffsetWrapper ow = entry.getValue();
                long brokerOffset = ow.getBrokerOffset();
                long consumeOffset = ow.getConsumerOffset();
                long diff = brokerOffset - consumeOffset;
                String topic = mq.getTopic();
                String brokerName = mq.getBrokerName();
                String brokerIdc = getBrokerIdc(brokerName);
                String key = brokerName + SPLIT + topic;
                brokerConsumerOffsetMap.put(key, brokerConsumerOffsetMap.getOrDefault(key, 0L) + consumeOffset);
                brokerConsumerDiffMap.put(key, brokerConsumerDiffMap.getOrDefault(key, 0L) + diff);
                metrics.add(new GroupQueueOffsetMetric(
                        group, topic, brokerName, brokerIdc, mq.getQueueId(), consumeOffset)
                );
                metrics.add(new GroupQueueLagMetric(
                        group, topic, brokerName, brokerIdc, mq.getQueueId(), diff)
                );
            }
            for (String key : brokerConsumerDiffMap.keySet()) {
                String[] split = key.split(SPLIT);
                if (split.length != 2) {
                    log.warn("集群group采集，topic名称异常, {}, group:{}", Arrays.stream(split).toList(), group);
                    continue;
                }
                String brokerName = split[0];
                String brokerIdc = getBrokerIdc(brokerName);
                String topic = split[1];
                metrics.add(new GroupBrokerOffsetMetric(
                        group, topic, brokerName, brokerIdc, brokerConsumerOffsetMap.get(key))
                );
                metrics.add(new GroupBrokerLagMetric(
                        group, topic, brokerName, brokerIdc, brokerConsumerDiffMap.get(key))
                );
            }
        }
        RedisQueueOffset.getGroupInstance().asyncSendAndSet(currentOffsetTableList);
        return metrics;
    }

    private static String getBrokerIdc(String brokerName) {
        return TurboClient.getInstance().getBrokerIdc(brokerName);
    }

    public static List<AbstractMetric> makeBroadCastGroupMetrics(List<TaskInfo<BroadCastConsumeStatsDTO>> taskInfoList) {
        List<AbstractMetric> metrics = new LinkedList<>();
        for (TaskInfo<BroadCastConsumeStatsDTO> taskInfo : taskInfoList) {
            String group = taskInfo.getTaskId();
            for (BrokerData brokerData : taskInfo.getBrokerDataList()) {
                metrics.add(new BroadCastGroupStatsBrokerMetric(group, brokerData.getBrokerName()));
            }
            BroadCastConsumeStatsDTO result = taskInfo.getResult();
            var onlineTable = result.getOnlineStats().getBroadCastOffsetTable();
            var offlineTable = result.getOfflineStats().getBroadCastOffsetTable();
            metrics.addAll(makeBroadCastGroupMetrics(group, onlineTable, true));
            metrics.addAll(makeBroadCastGroupMetrics(group, offlineTable, false));
            metrics.addAll(makeGroupBrokerStatsMetrics(group, taskInfo, MessageModel.BROADCASTING));
        }
        return metrics;
    }

    private static <T extends GroupBrokerStatsStorage> List<AbstractMetric>
    makeGroupBrokerStatsMetrics(String group, TaskInfo<T> taskInfo, MessageModel messageModel) {
        String consumeType = RedisStorage.getConsumeType(group);
        List<AbstractMetric> metrics = new LinkedList<>();
        GroupBrokerStatsStorage result = taskInfo.getResult();
        for (BrokerData brokerData : taskInfo.getBrokerDataList()) {
            String brokerName = brokerData.getBrokerName();
            String brokerIdc = getBrokerIdc(brokerName);
            String addr = ViewBrokerStatsDataFunc.getAddrFunc().apply(brokerData);
            Map<String/* topic */, BrokerStatsData> brokerStatsDatas = result.getBrokerSizeStatsDatas(addr);
            brokerStatsDatas.forEach((topic, bs) -> {
                long sum = bs.getStatsMinute().getSum();
                double tps = bs.getStatsMinute().getTps();
                metrics.add(new ConsumeSizeMetric(group, topic, brokerName, brokerIdc, messageModel, consumeType, sum));
                metrics.add(new ConsumeSizeTPSMetric(group, topic, brokerName, brokerIdc, messageModel, consumeType, tps));
            });
            Map<String, BrokerStatsData> numsStatsDatas = result.getBrokerNumsStatsDatas(addr);
            numsStatsDatas.forEach((topic, bs) -> {
                long sum = bs.getStatsMinute().getSum();
                double tps = bs.getStatsMinute().getTps();
                metrics.add(new ConsumeNumsMetric(group, topic, brokerName, brokerIdc, messageModel, consumeType, sum));
                metrics.add(new ConsumeNumsTPSMetric(group, topic, brokerName, brokerIdc, messageModel, consumeType, tps));
            });
            metrics.add(new GroupRouteMetric(group, brokerName));
        }
        return metrics;
    }

    private static List<AbstractMetric> makeBroadCastGroupMetrics(String group,
                                                                  HashMap<String, HashMap<MessageQueue, OffsetWrapper>> clientTable,
                                                                  boolean online) {
        List<AbstractMetric> metrics = new LinkedList<>();
        for (var cEntry : clientTable.entrySet()) {
            String cid = cEntry.getKey();
            HashMap<MessageQueue, OffsetWrapper> queueMap = cEntry.getValue();
            Map<String, Pair<Long, Long>> offsetMap = new HashMap<>();
            for (var entry : queueMap.entrySet()) {
                MessageQueue mq = entry.getKey();
                OffsetWrapper ow = entry.getValue();
                String brokerName = mq.getBrokerName();
                String topic = mq.getTopic();
                String key = topic + SPLIT + brokerName;
                long brokerOffset = ow.getBrokerOffset();
                long consumerOffset = ow.getConsumerOffset();
                long diff = brokerOffset - consumerOffset;
                if (!offsetMap.containsKey(key)) {
                    offsetMap.put(key, new Pair<>(0L, 0L));
                }
                Pair<Long, Long> pair = offsetMap.get(key);
                pair.setObject1(pair.getObject1() + consumerOffset);
                pair.setObject2(pair.getObject2() + diff);
            }
            for (var entry : offsetMap.entrySet()) {
                String[] split = entry.getKey().split(SPLIT);
                if (split.length != 2) {
                    log.warn("广播group采集，topic名称异常, {}, group:{}", Arrays.stream(split).toList(), group);
                    continue;
                }
                String topic = split[0];
                String brokerName = split[1];
                String brokerIdc = getBrokerIdc(brokerName);
                Pair<Long, Long> pair = entry.getValue();
                metrics.add(new BroadCastGroupBrokerOffsetMetric(
                        group, topic, brokerName, brokerIdc, cid, pair.getObject1(), online
                ));
                metrics.add(new BroadCastGroupBrokerLagMetric(
                        group, topic, brokerName, brokerIdc, cid, pair.getObject2(), online
                ));
            }
        }
        return metrics;
    }
}
