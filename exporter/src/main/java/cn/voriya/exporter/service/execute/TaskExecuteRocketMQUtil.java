package cn.voriya.exporter.service.execute;


import cn.voriya.boot.utils.IdcUtil;
import cn.voriya.exporter.client.TurboClient;
import cn.voriya.exporter.config.ExporterConfig;
import cn.voriya.exporter.entity.enums.CollectionTaskType;
import com.alibaba.rocketmq.client.consumer.DefaultMQPullConsumer;
import com.alibaba.rocketmq.client.producer.DefaultMQProducer;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.rocketmq.common.message.MessageQueue;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
public class TaskExecuteRocketMQUtil {
    private static final String NAME_SERVER_ADDRESS = ExporterConfig.getInstance().getNameserver(); // RocketMQ NameServer 地址
    private static final String clusterName = TurboClient.getInstance().getClusterName();
    private static final String localAz = IdcUtil.getLocalAz();
    private static final DefaultMQProducer producer = getProducer();
    private static final DefaultMQPullConsumer consumer = getConsumer();
    private static final String producerGroupName = "TaskProducerGroup_" + localAz.toUpperCase();
    private static final String consumerGroupName = "TaskConsumerGroup_" + localAz.toUpperCase();

    private static DefaultMQProducer getProducer() {
        DefaultMQProducer producer = new DefaultMQProducer(producerGroupName);
        producer.setNamesrvAddr(NAME_SERVER_ADDRESS);
        try {
            producer.start();
        } catch (Exception e) {
            throw new RuntimeException("Failed to start RocketMQ producer", e);
        }
        return producer;
    }

    private static DefaultMQPullConsumer getConsumer() {
        DefaultMQPullConsumer consumer = new DefaultMQPullConsumer(consumerGroupName);
        consumer.setNamesrvAddr(NAME_SERVER_ADDRESS);
        try {
            consumer.start();
        } catch (Exception e) {
            throw new RuntimeException("Failed to start RocketMQ consumer", e);
        }
        return consumer;
    }

    public static String makeTaskExecuteTopic(String az, CollectionTaskType taskType) {
        return String.format("%s_%s_%s", az, taskType.name(), clusterName);
    }

    public static void send(CollectionTaskType taskType, String taskId) {
        String topic = TaskExecuteRocketMQUtil.makeTaskExecuteTopic(localAz, taskType);
        // 构造消息
        Message message = new Message(topic, taskId.getBytes(StandardCharsets.UTF_8));
        try {
            producer.send(message);
        } catch (Throwable e) {
            log.error("提交任务id到RocketMQ失败, 集群:{}, taskType:{}, taskId:{}, topic:{}",
                    clusterName, taskType, taskId, topic, e);
        }
    }

    /**
     * 批量提交任务到 RocketMQ
     *
     * @param taskType 任务类型
     * @param taskIds  任务 ID 列表
     */
    public static void send(CollectionTaskType taskType, List<String> taskIds) {
        String topic = TaskExecuteRocketMQUtil.makeTaskExecuteTopic(localAz, taskType);

        // 构造消息列表
        List<Message> messages = new ArrayList<>();
        for (String taskId : taskIds) {
            Message message = new Message(topic, taskId.getBytes(StandardCharsets.UTF_8));
            messages.add(message);
        }

        try {
            // 批量发送消息
            SendResult sendResult = producer.send(messages);
            log.info("批量提交任务到RocketMQ成功, 集群:{}, taskType:{}, topic:{}, 任务数量:{}",
                    clusterName, taskType, topic, taskIds.size());
        } catch (Throwable e) {
            log.error("批量提交任务到RocketMQ失败, 集群:{}, taskType:{}, topic:{}, 任务数量:{}",
                    clusterName, taskType, topic, taskIds.size(), e);
        }
    }

    public static List<String> fetch(CollectionTaskType taskType, int count) {
        List<String> list = new ArrayList<>(count);
        try {
            // 获取主题的消息队列
            Set<MessageQueue> messageQueues = consumer.fetchSubscribeMessageQueues("TopicTest");
            for (MessageQueue mq : messageQueues) {
                long offset = consumer.fetchConsumeOffset(mq, true); // 获取队列的消费偏移量
                // 拉取消息，最多拉取32条
                List<MessageExt> messageExtList = consumer.pull(mq, "*", offset, count).getMsgFoundList();
                messageExtList.forEach(message -> {
                    String taskId = new String(message.getBody(), StandardCharsets.UTF_8);
                    list.add(taskId);
                });
                // 更新消费偏移量
                offset += count;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 消费逻辑（需要实现具体的消息拉取和解析）
        // 此处省略实际消费代码
        return list;
    }
}
