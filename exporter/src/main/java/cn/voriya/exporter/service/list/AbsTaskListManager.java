package cn.voriya.exporter.service.list;

import cn.voriya.exporter.entity.enums.CollectionTaskType;

import java.util.Set;

/**
 * Interface for managing task lists in a distributed environment.
 * Provides methods to retrieve and manage tasks for specific clusters and task types.
 */
public abstract class AbsTaskListManager {

    /**
     * Retrieves the list of tasks for the specified task type.
     * If no tasks are available, an empty set is returned.
     *
     * @param taskType    The type of the collection task.
     * @return A set of task IDs for the specified task type.
     */
    public abstract Set<String> getTaskList(CollectionTaskType taskType);

    /**
     * Resets the task list for the specified task type.
     * This method replaces the existing task list with the given set of task IDs.
     *
     * @param taskType    The type of the collection task.
     * @param taskIds     The new set of task IDs to reset the task list.
     */
    public abstract void resetTaskList(CollectionTaskType taskType, Set<String> taskIds);

    /**
     * Clears all tasks for the specified task type.
     *
     * @param taskType    The type of the collection task.
     * @param clusterName The name of the cluster.
     */
    public abstract void clearTaskList(CollectionTaskType taskType, String clusterName);

    /**
     * Checks if a specific task ID exists in the task list for the specified task type.
     *
     * @param taskType The type of the collection task.
     * @param taskId   The task ID to check for existence.
     * @return true if the task ID exists in the task list, false otherwise.
     */
    public abstract boolean containsTask(CollectionTaskType taskType, String taskId);

    public void clearTaskList(String clusterName){
        this.clearTaskList(CollectionTaskType.TOPIC, clusterName);
        this.clearTaskList(CollectionTaskType.GROUP, clusterName);
        this.clearTaskList(CollectionTaskType.BROAD_CAST_GROUP, clusterName);
    }
}
