package cn.voriya.exporter.service.node;

import cn.voriya.boot.cache.RedisCache;
import cn.voriya.boot.utils.IdUtil;
import cn.voriya.exporter.client.TurboClient;
import cn.voriya.exporter.config.RemoteConfig;
import cn.voriya.exporter.entity.enums.CollectionTaskType;
import cn.voriya.exporter.utils.RedisKeyUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class PollingCollectionNodeManagerBaseRedisHash extends AbsPollingCollectionNodeManager {

    private final String clusterName = TurboClient.getInstance().getClusterName();

    private static String getKey(CollectionTaskType taskType, String clusterName) {
        return RedisKeyUtil.makeTaskExecuteHashLockKey(taskType, clusterName);
    }

    /**
     * 格式化时间戳: yyyy-MM-dd HH:mm:ss.SSS
     *
     * @param timestamp The timestamp to format.
     * @return The formatted timestamp.
     */
    private static String formatTimestamp(long timestamp) {
        return String.format("%tF %tT.%tL", timestamp, timestamp, timestamp);
    }

    /**
     * 判断当前是否有节点正在采集，只统计 ttlSeconds 以内的节点，超时的节点顺便清理掉
     *
     * @param taskType The type of the collection task.
     * @return true if there are unfinished tasks, false otherwise.
     */
    @Override
    public boolean hasUnfinishedTaskNode(CollectionTaskType taskType) {
        try {
            // 判断 Redis 中是否存在未完成的任务节点
            long now = System.currentTimeMillis();
            long minTimestamp = now - RemoteConfig.getInstance().getNodeLockTtlSeconds().getLocalValue() * 1000;
            Map<String, String> map = RedisCache.getClient().hgetAll(getKey(taskType, clusterName));
            int removeCount = 0;
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String k = entry.getKey();
                String v = entry.getValue();
                long timestamp = Long.parseLong(v);
                if (timestamp < minTimestamp) {
                    removeCount++;
                    RedisCache.getClient().hdel(getKey(taskType, clusterName), k);
                    log.info("清理超时任务节点, taskType: {}, clusterName: {}, {}({})",
                            taskType, clusterName, k, formatTimestamp(timestamp));
                }
            }
            return map.size() > removeCount;
        } catch (Throwable e) {
            // 捕捉异常并记录错误日志
            log.error("从 Redis 判断【当前是否还有节点正在采集】失败, 默认当作没有节点正在采集, taskType: {}, clusterName: {}",
                    taskType, clusterName, e);
            return false;
        }
    }

    @Override
    public void startTask(CollectionTaskType taskType) {
        try {
            // 添加当前节点到 Redis 的任务锁集合中，标记任务开始
            RedisCache.getClient().hset(
                    getKey(taskType, clusterName),
                    IdUtil.getClientId(),
                    String.valueOf(System.currentTimeMillis())
            );
        } catch (Throwable e) {
            // 捕捉异常并记录错误日志
            log.error("向 Redis 添加任务节点失败, taskType: {}, clusterName: {}, clientId: {}",
                    taskType, clusterName, IdUtil.getClientId(), e);
        }
    }

    @Override
    public void completeTask(CollectionTaskType taskType) {
        try {
            // 从 Redis 的任务锁集合中移除当前节点，标记任务完成
            RedisCache.getClient().hdel(getKey(taskType, clusterName), IdUtil.getClientId());
        } catch (Throwable e) {
            // 捕捉异常并记录错误日志
            log.error("从 Redis 移除任务节点失败, taskType: {}, clusterName: {}, clientId: {}",
                    taskType, clusterName, IdUtil.getClientId(), e);
        }
    }

    @Override
    public void cleanTaskNode(CollectionTaskType taskType, String clusterName) {
        try {
            // 删除 Redis 中任务锁集合，清理所有任务节点
            RedisCache.getClient().del(getKey(taskType, clusterName));
        } catch (Throwable e) {
            // 捕捉异常并记录错误日志
            log.error("清理 Redis 中的任务节点失败, taskType: {}, clusterName: {}",
                    taskType, clusterName, e);
        }
    }

    @Override
    public void cleanTaskNode(CollectionTaskType taskType) {
        cleanTaskNode(taskType, clusterName);
    }

    /**
     * 获取任务节点列表，格式为: clientId(timestamp)
     *
     * @param taskType    The type of the collection task.
     * @param clusterName The name of the cluster.
     * @return A set of task nodes.
     */
    @Override
    public Set<String> getTaskNode(CollectionTaskType taskType, String clusterName) {
        Map<String, String> map = RedisCache.getClient().hgetAll(getKey(taskType, clusterName));
        return map.entrySet().stream()
                .map(entry -> "%s(%s)".formatted(entry.getKey(), formatTimestamp(Long.parseLong(entry.getValue()))))
                .collect(Collectors.toSet());
    }
}
