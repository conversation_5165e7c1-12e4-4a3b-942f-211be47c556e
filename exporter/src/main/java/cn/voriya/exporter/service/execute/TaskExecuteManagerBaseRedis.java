package cn.voriya.exporter.service.execute;

import cn.voriya.boot.cache.RedisCache;
import cn.voriya.boot.cache.lua.LuaGetAndDelTask;
import cn.voriya.exporter.entity.enums.CollectionTaskType;
import cn.voriya.exporter.utils.RedisKeyUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class TaskExecuteManagerBaseRedis extends AbsTaskExecuteManager {

    @Override
    protected void submitTask(CollectionTaskType taskType, List<String> taskIds, Set<String> brokersAz) {
        Map<String, Double> taskMap = new HashMap<>();
        for (String taskId : taskIds) {
            taskMap.put(taskId, 0.0);
        }
        for (String az : brokersAz) {
            RedisCache.getClient().zadd(RedisKeyUtil.makeTaskExecuteKey(az, taskType, clusterName), taskMap);
        }
    }

    @Override
    protected Set<String> fetchTask(CollectionTaskType taskType, int count, String localAz) {
        List<String> list = new LuaGetAndDelTask(RedisKeyUtil.makeTaskExecuteKey(localAz, taskType, clusterName), count).execute();
        return new HashSet<>(list);
    }

    @Override
    protected void cleanTask(CollectionTaskType taskType, String clusterName, Set<String> brokersAz) {
        for (String az : brokersAz) {
            RedisCache.getClient().del(RedisKeyUtil.makeTaskExecuteKey(az, taskType, clusterName));
        }
    }
}
