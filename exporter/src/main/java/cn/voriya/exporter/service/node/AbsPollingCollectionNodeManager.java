package cn.voriya.exporter.service.node;

import cn.voriya.boot.utils.IdUtil;
import cn.voriya.exporter.entity.enums.CollectionTaskType;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Set;

/**
 * Abstract class for managing polling collection nodes and tasks in a distributed environment.
 * Provides methods for task lifecycle management, querying task states, and cleaning up resources.
 */
@Slf4j
public abstract class AbsPollingCollectionNodeManager {

    public AbsPollingCollectionNodeManager() {
        // 在启动时直接删除该集群的所有锁，防止上次的锁影响本次启动
        this.cleanTaskNode();
        log.info("{} 启动时锁清理完成", IdUtil.getClientId());

        // 注册ShutdownHook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("{} 程序退出，开始清理资源...", IdUtil.getClientId());
            this.completeTask();
            log.info("{} 资源清理完成", IdUtil.getClientId());
        }));
    }

    /**
     * Checks whether there are any unfinished tasks for the given task type and cluster name.
     *
     * @param taskType    The type of the collection task.
     * @return {@code true} if there are unfinished tasks, {@code false} otherwise.
     */
    public abstract boolean hasUnfinishedTaskNode(CollectionTaskType taskType);

    /**
     * Starts a new task for the given task type and cluster name.
     * Registers the current client as a participant in the task.
     *
     * @param taskType    The type of the collection task.
     */
    public abstract void startTask(CollectionTaskType taskType);

    /**
     * Marks a specific task as complete for the given task type and cluster name.
     * Removes the current client from the task participant list.
     *
     * @param taskType    The type of the collection task.
     */
    public abstract void completeTask(CollectionTaskType taskType);

    /**
     * Cleans up all nodes associated with the specified task type and cluster name.
     * This action removes all participants from the task list in Redis.
     *
     * @param taskType    The type of the collection task.
     * @param clusterName The name of the cluster.
     */
    public abstract void cleanTaskNode(CollectionTaskType taskType, String clusterName);

    /**
     * Cleans up all nodes associated with the specified task type for self cluster.
     * @param taskType    The type of the collection task.
     */
    public abstract void cleanTaskNode(CollectionTaskType taskType);

    /**
     * Retrieves the list of nodes currently participating in a specific task type for the given cluster name.
     *
     * @param taskType    The type of the collection task.
     * @param clusterName The name of the cluster.
     * @return A set of node IDs participating in the task.
     */
    public abstract Set<String> getTaskNode(CollectionTaskType taskType, String clusterName);

    /**
     * Marks all tasks as complete for the given cluster name.
     * Removes the current client from all task participant lists for the cluster.
     *
     */
    public final void completeTask() {
        this.completeTask(CollectionTaskType.TOPIC);
        this.completeTask(CollectionTaskType.GROUP);
        this.completeTask(CollectionTaskType.BROAD_CAST_GROUP);
    }

    /**
     * Cleans up all nodes for all task types associated with the self cluster.
     * This action removes all participants from all task lists in Redis.
     */
    public final void cleanTaskNode() {
        this.cleanTaskNode(CollectionTaskType.TOPIC);
        this.cleanTaskNode(CollectionTaskType.GROUP);
        this.cleanTaskNode(CollectionTaskType.BROAD_CAST_GROUP);
    }

    /**
     * Cleans up all nodes for all task types associated with the specified cluster name.
     * This action removes all participants from all task lists in Redis.
     *
     * @param clusterName The name of the cluster.
     */
    public final void cleanTaskNode(String clusterName) {
        this.cleanTaskNode(CollectionTaskType.TOPIC, clusterName);
        this.cleanTaskNode(CollectionTaskType.GROUP, clusterName);
        this.cleanTaskNode(CollectionTaskType.BROAD_CAST_GROUP, clusterName);
    }

    /**
     * Retrieves the list of nodes currently participating in all task types for the given cluster name.
     *
     * @return A map where the keys are task types and the values are sets of node IDs participating in the tasks.
     */
    public final Map<CollectionTaskType, Set<String>> getTaskNode(String clusterName) {
        return Map.of(
                CollectionTaskType.TOPIC, this.getTaskNode(CollectionTaskType.TOPIC, clusterName),
                CollectionTaskType.GROUP, this.getTaskNode(CollectionTaskType.GROUP, clusterName),
                CollectionTaskType.BROAD_CAST_GROUP, this.getTaskNode(CollectionTaskType.BROAD_CAST_GROUP, clusterName)
        );
    }
}
