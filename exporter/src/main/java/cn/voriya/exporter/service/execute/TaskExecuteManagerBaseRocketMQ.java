package cn.voriya.exporter.service.execute;

import cn.voriya.exporter.entity.enums.CollectionTaskType;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
public class TaskExecuteManagerBaseRocketMQ extends AbsTaskExecuteManager {

    @Override
    protected void submitTask(CollectionTaskType taskType, List<String> taskIds, Set<String> brokersAz) {

    }

    @Override
    public Set<String> fetchTask(CollectionTaskType taskType, int count, String localAz) {
        Set<String> fetchedTasks = new HashSet<>();
        try {
            // 在 RocketMQ 中实现任务获取需要额外的逻辑（如消费消息队列）
            List<String> messages = TaskExecuteRocketMQUtil.fetch(taskType, count);
            fetchedTasks.addAll(messages);
        } catch (Exception e) {
            log.error("从RocketMQ获取任务列表失败, taskType={}, count={}, 集群:{}", taskType, count, clusterName, e);
        }
        return fetchedTasks;
    }

    /**
     * 不支持
     *
     * @param taskType    The type of the collection task.
     * @param clusterName The name of the cluster.
     */
    @Override
    public void cleanTask(CollectionTaskType taskType, String clusterName, Set<String> brokersAz) {
        log.error("不支持从RocketMQ中清理任务列表, taskType={}, clusterName={}, brokersAz={}",
                taskType, clusterName, brokersAz);
    }
}
