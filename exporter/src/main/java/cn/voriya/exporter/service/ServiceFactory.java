package cn.voriya.exporter.service;

import cn.voriya.exporter.service.execute.AbsTaskExecuteManager;
import cn.voriya.exporter.service.execute.TaskExecuteManagerBaseRedis;
import cn.voriya.exporter.service.list.AbsTaskListManager;
import cn.voriya.exporter.service.list.TaskListManagerBaseRedis;
import cn.voriya.exporter.service.node.AbsPollingCollectionNodeManager;
import cn.voriya.exporter.service.node.PollingCollectionNodeManagerBaseRedisHash;

import java.util.HashMap;
import java.util.Map;

/**
 * Unified factory for managing different types of service instances.
 * Uses a map to store and manage instances for different service types.
 */
public class ServiceFactory {

    // 用于存储不同类型的服务实例
    private static final Map<Class<?>, Object> serviceInstances = new HashMap<>();

    static {
        registerService(AbsPollingCollectionNodeManager.class, new PollingCollectionNodeManagerBaseRedisHash());
        registerService(AbsTaskExecuteManager.class, new TaskExecuteManagerBaseRedis());
        registerService(AbsTaskListManager.class, new TaskListManagerBaseRedis());
    }

    /**
     * Registers a service instance for the specified type.
     * If an instance for the type already exists, it will be replaced.
     *
     * @param type     The class type of the service interface.
     * @param instance The service instance to register.
     * @param <T>      The type of the service interface.
     */
    public static <T> void registerService(Class<T> type, T instance) {
        if (type == null || instance == null) {
            throw new IllegalArgumentException("Type and instance cannot be null");
        }
        serviceInstances.put(type, instance);
    }

    /**
     * Retrieves the service instance for the specified type.
     * If no instance is registered for the type, an exception will be thrown.
     *
     * @param type The class type of the service interface.
     * @param <T>  The type of the service interface.
     * @return The service instance for the specified type.
     */
    public static <T> T getService(Class<T> type) {
        if (type == null) {
            throw new IllegalArgumentException("Type cannot be null");
        }
        Object instance = serviceInstances.get(type);
        if (instance == null) {
            throw new IllegalStateException("No service registered for type: " + type.getName());
        }
        return type.cast(instance);
    }
}
