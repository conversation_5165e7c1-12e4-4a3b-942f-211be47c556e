package cn.voriya.exporter.service.node;

import cn.voriya.boot.cache.RedisCache;
import cn.voriya.boot.utils.IdUtil;
import cn.voriya.exporter.client.TurboClient;
import cn.voriya.exporter.entity.enums.CollectionTaskType;
import cn.voriya.exporter.utils.RedisKeyUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

@Slf4j
public class PollingCollectionNodeManagerBaseRedis extends AbsPollingCollectionNodeManager {

    private final String clusterName = TurboClient.getInstance().getClusterName();

    @Override
    public boolean hasUnfinishedTaskNode(CollectionTaskType taskType) {
        try {
            // 判断 Redis 中是否存在未完成的任务节点
            return RedisCache.getClient().scard(RedisKeyUtil.makeTaskExecuteLockKey(taskType, clusterName)) != 0;
        } catch (Throwable e) {
            // 捕捉异常并记录错误日志
            log.error("从 Redis 判断【当前是否还有节点正在采集】失败, 默认当作没有节点正在采集, taskType: {}, clusterName: {}",
                    taskType, clusterName, e);
            return false;
        }
    }

    @Override
    public void startTask(CollectionTaskType taskType) {
        try {
            // 添加当前节点到 Redis 的任务锁集合中，标记任务开始
            RedisCache.getClient().sadd(RedisKeyUtil.makeTaskExecuteLockKey(taskType, clusterName), IdUtil.getClientId());
        } catch (Throwable e) {
            // 捕捉异常并记录错误日志
            log.error("向 Redis 添加任务节点失败, taskType: {}, clusterName: {}, clientId: {}",
                    taskType, clusterName, IdUtil.getClientId(), e);
        }
    }

    @Override
    public void completeTask(CollectionTaskType taskType) {
        try {
            // 从 Redis 的任务锁集合中移除当前节点，标记任务完成
            RedisCache.getClient().srem(RedisKeyUtil.makeTaskExecuteLockKey(taskType, clusterName), IdUtil.getClientId());
        } catch (Throwable e) {
            // 捕捉异常并记录错误日志
            log.error("从 Redis 移除任务节点失败, taskType: {}, clusterName: {}, clientId: {}",
                    taskType, clusterName, IdUtil.getClientId(), e);
        }
    }

    @Override
    public void cleanTaskNode(CollectionTaskType taskType, String clusterName) {
        try {
            // 删除 Redis 中任务锁集合，清理所有任务节点
            RedisCache.getClient().del(RedisKeyUtil.makeTaskExecuteLockKey(taskType, clusterName));
        } catch (Throwable e) {
            // 捕捉异常并记录错误日志
            log.error("清理 Redis 中的任务节点失败, taskType: {}, clusterName: {}",
                    taskType, clusterName, e);
        }
    }

    @Override
    public void cleanTaskNode(CollectionTaskType taskType) {
        cleanTaskNode(taskType, clusterName);
    }

    @Override
    public Set<String> getTaskNode(CollectionTaskType taskType, String clusterName) {
        return RedisCache.getClient().smembers(RedisKeyUtil.makeTaskExecuteLockKey(taskType, clusterName));
    }
}
