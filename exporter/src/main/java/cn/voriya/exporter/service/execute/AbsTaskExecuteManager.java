package cn.voriya.exporter.service.execute;

import cn.voriya.boot.utils.IdcUtil;
import cn.voriya.boot.utils.PageUtil;
import cn.voriya.exporter.client.TurboClient;
import cn.voriya.exporter.entity.enums.CollectionTaskType;
import cn.voriya.exporter.service.ServiceFactory;
import cn.voriya.exporter.service.list.AbsTaskListManager;
import cn.voriya.exporter.service.node.AbsPollingCollectionNodeManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Interface for task execution and management in a distributed environment.
 * Provides methods for submitting, fetching, and cleaning tasks.
 */
@Slf4j
public abstract class AbsTaskExecuteManager {

    protected final String clusterName = TurboClient.getInstance().getClusterName();

    /**
     * Submits a set of task members for execution to the distributed cache.
     * This method is implemented by the concrete task execution manager.
     *
     * @param taskType  The type of the collection task.
     * @param taskIds   The list of task IDs to be submitted.
     * @param brokersAz The set of availability zones for the brokers.
     */
    protected abstract void submitTask(CollectionTaskType taskType, List<String> taskIds, Set<String> brokersAz);

    /**
     * Fetches a specified number of tasks for execution from the specified AZ (availability zone).
     * Removes the fetched tasks from the task list.
     *
     * @param taskType The type of the collection task.
     * @param count    The number of tasks to fetch.
     * @param localAz  The collector node's availability zone.
     * @return A set of task IDs fetched for execution.
     */
    protected abstract Set<String> fetchTask(CollectionTaskType taskType, int count, String localAz);

    /**
     * Cleans up all tasks for a specific task type and cluster name.
     * Removes all task entries from the distributed cache for the task type and cluster name.
     *
     * @param taskType    The type of the collection task.
     * @param clusterName The name of the cluster.
     * @param brokersAz   The set of availability zones for the brokers.
     */
    protected abstract void cleanTask(CollectionTaskType taskType, String clusterName, Set<String> brokersAz);

    /**
     * Cleans up all tasks for a specific task type and cluster name.
     * Removes all task entries from the distributed cache for the task type and cluster name.
     *
     * @param taskType    The type of the collection task.
     * @param clusterName The name of the cluster.
     */
    public final void cleanTask(CollectionTaskType taskType, String clusterName) {
        Set<String> brokersAz = TurboClient.getInstance().getClusterBrokerAzs();
        cleanTask(taskType, clusterName, brokersAz);
    }

    /**
     * Cleans up all tasks for all task types in a specific cluster.
     * Removes all task entries and associated nodes from the distributed cache for the cluster.
     *
     * @param clusterName The name of the cluster.
     */
    public final void cleanTask(String clusterName) {
        var collectionNodeManager = ServiceFactory.getService(AbsPollingCollectionNodeManager.class);
        collectionNodeManager.cleanTaskNode(clusterName);
        this.cleanTask(CollectionTaskType.TOPIC, clusterName);
        this.cleanTask(CollectionTaskType.GROUP, clusterName);
        this.cleanTask(CollectionTaskType.BROAD_CAST_GROUP, clusterName);
    }

    /**
     * Submits a set of task members for execution to the distributed cache.
     * This method processes the members in pages to avoid overloading the cache.
     *
     * @param taskType    The type of the collection task.
     * @param members     The set of task members to be submitted.
     */
    private void submitTask(CollectionTaskType taskType, Set<String> members) {
        Set<String> brokersAz = TurboClient.getInstance().getClusterBrokerAzs();
        for (List<String> pagedTaskIds : PageUtil.getPages(500, new ArrayList<>(members))) {
            submitTask(taskType, pagedTaskIds, brokersAz);
        }
    }

    /**
     * Submits a task for execution based on the given task type and cluster name.
     * If there is an unfinished task, it returns an error message.
     * Otherwise, it processes the task and logs the execution time details.
     *
     * @param taskType    The type of the collection task.
     * @return A message indicating the success or failure of the task submission.
     */
    public final String submitTask(CollectionTaskType taskType) {
        StopWatch stopWatch = StopWatch.createStarted();
        var collectionNodeManager = ServiceFactory.getService(AbsPollingCollectionNodeManager.class);
        var taskListManager = ServiceFactory.getService(AbsTaskListManager.class);
        if (collectionNodeManager.hasUnfinishedTaskNode(taskType)) {
            String res = "Last task is not finished, %s, cluster: %s".formatted(taskType, clusterName);
            log.error(res);
            return res;
        }
        stopWatch.split();
        long lockedCost = stopWatch.getSplitTime();
        Set<String> taskList = taskListManager.getTaskList(taskType);
        if (taskList.isEmpty()) {
            String res = "No task to execute, %s, cluster: %s".formatted(taskType, clusterName);
            log.warn(res);
            return res;
        }
        stopWatch.split();
        long getCost = stopWatch.getSplitTime();
        submitTask(taskType, taskList);
        stopWatch.split();
        long addCost = stopWatch.getSplitTime();
        String res = String.format("success %s, cluster: %s, lockedCost:%sms, getCost:%sms, addCost:%sms, totalCost:%sms",
                taskType, clusterName, lockedCost, getCost, addCost, stopWatch.getTime());
        log.info(res);
        return res;
    }

    /**
     * Fetches a specified number of tasks for execution from the local AZ (availability zone).
     * Removes the fetched tasks from the task list.
     *
     * @param taskType    The type of the collection task.
     * @param count       The number of tasks to fetch.
     * @return A set of task IDs fetched for execution.
     */

    public final Set<String> fetchTask(CollectionTaskType taskType, int count) {
        String localAz = IdcUtil.getLocalAz();
        return fetchTask(taskType, count, localAz);
    }
}
