package cn.voriya.exporter.service.execute;

import java.util.concurrent.ArrayBlockingQueue;

public class BlockingContainer<T> {
    private final ArrayBlockingQueue<T> queue;

    public BlockingContainer(int capacity) {
        this.queue = new ArrayBlockingQueue<>(capacity);
    }

    /**
     * 存入元素（阻塞操作）。
     * 如果线程被中断，不对外抛出异常，恢复线程中断状态。
     *
     * @param element 要存入的元素。
     */
    public void put(T element) {
        try {
            queue.put(element); // 阻塞式存入
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复线程中断状态
        }
    }

    /**
     * 一次性取出所有元素。
     * 如果操作失败，返回空数组。
     *
     * @return 所有取出的元素。
     */
    public T[] takeAll() {
        try {
            // 获取队列的元素并转换为数组
            T[] elements = (T[]) queue.toArray(new Object[0]); // 使用 Object[] 并强制转换为 T[]
            queue.clear(); // 清空队列
            return elements;
        } catch (Exception e) {
            // 如果发生异常，返回空数组
            return (T[]) new Object[0]; // 使用 Object[] 并强制转换为 T[]
        }
    }
}
