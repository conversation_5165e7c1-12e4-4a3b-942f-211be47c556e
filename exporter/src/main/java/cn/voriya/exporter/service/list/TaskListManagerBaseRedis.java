package cn.voriya.exporter.service.list;

import cn.voriya.boot.cache.RedisCache;
import cn.voriya.exporter.client.TurboClient;
import cn.voriya.exporter.entity.enums.CollectionTaskType;
import cn.voriya.exporter.utils.RedisKeyUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class TaskListManagerBaseRedis extends AbsTaskListManager {

    /**
     * 在redis出问题的时候，可以先使用内存数据
     */
    private final Map<CollectionTaskType, Set<String>> memoryCache = new ConcurrentHashMap<>();
    private final String clusterName = TurboClient.getInstance().getClusterName();

    private static String getKey(CollectionTaskType taskType, String clusterName) {
        return RedisKeyUtil.makeTaskListKey(taskType, clusterName);
    }

    @Override
    public void clearTaskList(CollectionTaskType taskType, String clusterName) {
        RedisCache.getClient().del(getKey(taskType, clusterName));
    }

    @Override
    public void resetTaskList(CollectionTaskType taskType, Set<String> taskIds) {
        memoryCache.put(taskType, new HashSet<>(taskIds));
        RedisCache.resetSetMembers(getKey(taskType, clusterName), taskIds);
    }

    @Override
    public Set<String> getTaskList(CollectionTaskType taskType) {
        try {
            return RedisCache.getClient().sscan(getKey(taskType, clusterName), "*");
        } catch (Throwable e) {
            Set<String> taskList = memoryCache.get(taskType);
            log.error("从redis获取任务{}列表失败, 使用内存数据替代返回({}), 集群:{}", taskType, taskList.size(), clusterName, e);
            return taskList;
        }
    }

    @Override
    public boolean containsTask(CollectionTaskType taskType, String taskId) {
        try {
            return RedisCache.getClient().sismember(getKey(taskType, clusterName), taskId);
        } catch (Throwable e) {
            Set<String> taskList = memoryCache.get(taskType);
            boolean result = taskList != null && taskList.contains(taskId);
            log.error("从redis检查任务{}是否存在失败, 使用内存数据替代返回({}), 集群:{}, taskId:{}", taskType, result, clusterName, taskId, e);
            return result;
        }
    }
}
