package cn.voriya.exporter;

import cn.voriya.boot.VoriyaBoot;
import cn.voriya.boot.http.VoriyaHttpServer;
import cn.voriya.exporter.cache.impl.TCBaseClient;
import cn.voriya.exporter.client.TurboClient;
import cn.voriya.exporter.config.ExporterConfig;
import cn.voriya.exporter.tasks.TaskChain;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Application {
    public static void main() {
        Application.initialize();
        String clusterName = TurboClient.getInstance().getClusterName();
        int port = ExporterConfig.getInstance().getPort();
        try (var ignored = VoriyaHttpServer.create(port)) {
            TaskChain.start();
            log.info("TurboMQ Pusher started, clusterName: {}, port: {}", clusterName, port);
            Thread.currentThread().join();
        } catch (Throwable t) {
            log.error("Exception starting, clusterName: {}, port: {}", clusterName, port, t);
        } finally {
            log.info("TurboMQ Pusher exiting, clusterName: {}, port: {}", clusterName, port);
        }
    }

    public static void initialize() {
        VoriyaBoot.initialize(TCBaseClient.getInstance());
        ExporterConfig.initialize();
        TurboClient.initialize();
        VoriyaBoot.registerClusterName(TurboClient.getInstance().getClusterName());
        VoriyaBoot.scanDispatchers(Application.class.getPackageName());
        VoriyaBoot.removeDefaultGlobalMetricTag("ident");
    }
}
