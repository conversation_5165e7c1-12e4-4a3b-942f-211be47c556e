/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package cn.voriya.exporter.rocket;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.exporter.metrics.rocket.BrokerRuntimeMetric;
import com.alibaba.rocketmq.common.protocol.body.KVTable;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class RMQMetricsCollector {

    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeMsgPutTotalTodayNow = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeMsgGetTotalTodayNow = new HashMap<>();

    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeMsgGetTotalYesterdayMorning = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeMsgPutTotalYesterdayMorning = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeMsgGetTotalTodayMorning = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeMsgPutTotalTodayMorning = new HashMap<>();

    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeDispatchBehindBytes = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageSizeTotal = new HashMap<>();

    private final Map<BrokerRuntimeMetric, Double> brokerRuntimePutMessageAverageSize = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeQueryThreadPoolQueueCapacity = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeRemainTransientStoreBufferNumbs = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeEarliestMessageTimeStamp = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageEntireTimeMax = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeStartAcceptSendRequestTimeStamp = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeSendThreadPoolQueueSize = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageTimesTotal = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeGetMessageEntireTimeMax = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePageCacheLockTimeMills = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeCommitLogDiskRatio = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeConsumeQueueDiskRatio = new HashMap<>();

    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetFoundTps600 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetFoundTps60 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetFoundTps10 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetTotalTps600 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetTotalTps60 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetTotalTps10 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetTransferedTps600 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetTransferedTps60 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetTransferedTps10 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetMissTps600 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetMissTps60 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeGetMissTps10 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimePutTps600 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimePutTps60 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimePutTps10 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimePutLatency99 = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimePutLatency999 = new HashMap<>();

    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeDispatchMaxBuffer = new HashMap<>();

    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap10toMore = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap5to10s = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap4to5s = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap3to4s = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap2to3s = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap1to2s = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap500to1s = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap200to500ms = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap100to200ms = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap50to100ms = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap10to50ms = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap0to10ms = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePutMessageDistributeTimeMap0ms = new HashMap<>();

    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePullThreadPoolQueueCapacity = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeSendThreadPoolQueueCapacity = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePullThreadPoolQueueSize = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeQueryThreadPoolQueueSize = new HashMap<>();

    private final Map<BrokerRuntimeMetric, Long> brokerRuntimePullThreadPoolQueueHeadWaitTimeMills = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeQueryThreadPoolQueueHeadWaitTimeMills = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeSendThreadPoolQueueHeadWaitTimeMills = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeCommitLogDirCapacityFree = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeCommitLogDirCapacityTotal = new HashMap<>();

    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeCommitLogMaxOffset = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Long> brokerRuntimeCommitLogMinOffset = new HashMap<>();
    private final Map<BrokerRuntimeMetric, Double> brokerRuntimeRemainHowManyDataToFlush = new HashMap<>();


    public void addBrokerRuntimeStatsMetric(KVTable table, Long brokerId, String brokerAddress, String brokerName) {
        BrokerRuntimeStats stats = new BrokerRuntimeStats(table);
        addBrokerRuntimePutMessageDistributeTimeMap(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion(), stats);
        addCommitLogDirCapacity(brokerId, brokerAddress, brokerName, stats);
        addAllKindOfTps(brokerId, brokerAddress, brokerName, stats);

        brokerRuntimePutLatency99.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPutLatency99());

        brokerRuntimePutLatency999.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPutLatency999());

        brokerRuntimeMsgPutTotalTodayNow.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getMsgPutTotalTodayNow());

        brokerRuntimeMsgGetTotalTodayNow.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getMsgGetTotalTodayNow());

        brokerRuntimeMsgPutTotalTodayMorning.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getMsgPutTotalTodayMorning());
        brokerRuntimeMsgGetTotalTodayMorning.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getMsgGetTotalTodayMorning());
        brokerRuntimeMsgPutTotalYesterdayMorning.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getMsgPutTotalYesterdayMorning());
        brokerRuntimeMsgGetTotalYesterdayMorning.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getMsgGetTotalYesterdayMorning());
        brokerRuntimeSendThreadPoolQueueHeadWaitTimeMills.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getSendThreadPoolQueueHeadWaitTimeMills());
        brokerRuntimeQueryThreadPoolQueueHeadWaitTimeMills.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getQueryThreadPoolQueueHeadWaitTimeMills());
        brokerRuntimePullThreadPoolQueueHeadWaitTimeMills.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPullThreadPoolQueueHeadWaitTimeMills());
        brokerRuntimeQueryThreadPoolQueueSize.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getQueryThreadPoolQueueSize());
        brokerRuntimePullThreadPoolQueueSize.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPullThreadPoolQueueSize());
        brokerRuntimeSendThreadPoolQueueCapacity.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getSendThreadPoolQueueCapacity());
        brokerRuntimePullThreadPoolQueueCapacity.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPullThreadPoolQueueCapacity());

        brokerRuntimeRemainHowManyDataToFlush.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getRemainHowManyDataToFlush());
        brokerRuntimeCommitLogMinOffset.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getCommitLogMinOffset());
        brokerRuntimeCommitLogMaxOffset.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getCommitLogMaxOffset());


        brokerRuntimeDispatchMaxBuffer.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getDispatchMaxBuffer());
        brokerRuntimeConsumeQueueDiskRatio.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getConsumeQueueDiskRatio());
        brokerRuntimeCommitLogDiskRatio.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getCommitLogDiskRatio());
        brokerRuntimePageCacheLockTimeMills.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPageCacheLockTimeMills());
        brokerRuntimeGetMessageEntireTimeMax.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetMessageEntireTimeMax());
        brokerRuntimePutMessageTimesTotal.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPutMessageTimesTotal());
        brokerRuntimeSendThreadPoolQueueSize.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getSendThreadPoolQueueSize());
        brokerRuntimeStartAcceptSendRequestTimeStamp.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getStartAcceptSendRequestTimeStamp());
        brokerRuntimePutMessageEntireTimeMax.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPutMessageEntireTimeMax());
        brokerRuntimeEarliestMessageTimeStamp.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getEarliestMessageTimeStamp());
        brokerRuntimeRemainTransientStoreBufferNumbs.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getRemainTransientStoreBufferNumbs());
        brokerRuntimeQueryThreadPoolQueueCapacity.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getQueryThreadPoolQueueCapacity());
        brokerRuntimePutMessageAverageSize.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPutMessageAverageSize());
        brokerRuntimePutMessageSizeTotal.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPutMessageSizeTotal());
        brokerRuntimeDispatchBehindBytes.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getDispatchBehindBytes());
    }

    private void addAllKindOfTps(Long brokerId, String brokerAddress, String brokerHost, BrokerRuntimeStats stats) {
        brokerRuntimePutTps10.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPutTps().getTen());
        brokerRuntimePutTps60.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPutTps().getSixty());
        brokerRuntimePutTps600.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getPutTps().getSixHundred());
        brokerRuntimeGetMissTps10.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetMissTps().getTen());
        brokerRuntimeGetMissTps60.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetMissTps().getSixty());
        brokerRuntimeGetMissTps600.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetMissTps().getSixHundred());
        brokerRuntimeGetTransferedTps10.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetTransferedTps().getTen());
        brokerRuntimeGetTransferedTps60.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetTransferedTps().getSixty());
        brokerRuntimeGetTransferedTps600.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetTransferedTps().getSixHundred());
        brokerRuntimeGetTotalTps10.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetTotalTps().getTen());
        brokerRuntimeGetTotalTps60.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetTotalTps().getSixty());
        brokerRuntimeGetTotalTps600.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetTotalTps().getSixHundred());
        brokerRuntimeGetFoundTps10.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetFoundTps().getTen());
        brokerRuntimeGetFoundTps60.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetFoundTps().getSixty());
        brokerRuntimeGetFoundTps600.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetFoundTps().getSixHundred());
        brokerRuntimeGetFoundTps600.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getGetFoundTps().getSixHundred());
    }

    private void addCommitLogDirCapacity(Long brokerId, String brokerAddress, String brokerHost, BrokerRuntimeStats stats) {
        brokerRuntimeCommitLogDirCapacityTotal.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getCommitLogDirCapacityTotal());
        brokerRuntimeCommitLogDirCapacityFree.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerHost,
                stats.getBrokerVersionDesc(),
                stats.getBootTimestamp(),
                stats.getBrokerVersion()), stats.getCommitLogDirCapacityFree());
    }

    private void addBrokerRuntimePutMessageDistributeTimeMap(
            Long brokerId, String brokerAddress, String brokerName,
            String brokerDes, long bootTimestamp, int brokerVersion,
            BrokerRuntimeStats stats) {
        if (stats.getPutMessageDistributeTimeMap() == null || stats.getPutMessageDistributeTimeMap().isEmpty()) {
            log.warn("WARN putMessageDistributeTime is null or empty");
            return;
        }
        brokerRuntimePutMessageDistributeTimeMap0ms.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("<=0ms"));
        brokerRuntimePutMessageDistributeTimeMap0to10ms.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("0~10ms"));
        brokerRuntimePutMessageDistributeTimeMap10to50ms.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("10~50ms"));
        brokerRuntimePutMessageDistributeTimeMap50to100ms.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("50~100ms"));
        brokerRuntimePutMessageDistributeTimeMap100to200ms.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("100~200ms"));
        brokerRuntimePutMessageDistributeTimeMap200to500ms.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("200~500ms"));
        brokerRuntimePutMessageDistributeTimeMap500to1s.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("500ms~1s"));
        brokerRuntimePutMessageDistributeTimeMap1to2s.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("1~2s"));
        brokerRuntimePutMessageDistributeTimeMap2to3s.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("2~3s"));
        brokerRuntimePutMessageDistributeTimeMap3to4s.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("3~4s"));
        brokerRuntimePutMessageDistributeTimeMap4to5s.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("4~5s"));
        brokerRuntimePutMessageDistributeTimeMap5to10s.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("5~10s"));
        brokerRuntimePutMessageDistributeTimeMap10toMore.put(new BrokerRuntimeMetric(
                brokerId, brokerAddress, brokerName,
                brokerDes,
                bootTimestamp,
                brokerVersion), stats.getPutMessageDistributeTimeMap().get("10s~"));
    }

    private static <T extends Number> void loadBrokerRuntimeStatsMetric(BrokerRuntimeFamily family, Map.Entry<BrokerRuntimeMetric, T> entry) {
        family.addMetric(Arrays.asList(
                entry.getKey().getBrokerId().toString(),
                entry.getKey().getBrokerAddress(),
                entry.getKey().getBroker(),
                entry.getKey().getBrokerDes(),
                String.valueOf(entry.getKey().getBootTimestamp()),
                String.valueOf(entry.getKey().getBrokerVersion())
        ), entry.getValue().doubleValue());
    }

    private void collectBrokerRuntimeStatsPutMessageDistributeTime(List<BrokerRuntimeFamily> mfs) {
        BrokerRuntimeFamily pmdt0 = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_0ms");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap0ms.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt0, entry);
        }
        mfs.add(pmdt0);

        BrokerRuntimeFamily pmdt0to10ms = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_0to10ms");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap0to10ms.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt0to10ms, entry);
        }
        mfs.add(pmdt0to10ms);

        BrokerRuntimeFamily pmdt10to50ms = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_10to50ms");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap10to50ms.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt10to50ms, entry);
        }
        mfs.add(pmdt10to50ms);

        BrokerRuntimeFamily pmdt50to100ms = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_50to100ms");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap50to100ms.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt50to100ms, entry);
        }
        mfs.add(pmdt50to100ms);

        BrokerRuntimeFamily pmdt100to200ms = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_100to200ms");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap100to200ms.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt100to200ms, entry);
        }
        mfs.add(pmdt100to200ms);

        BrokerRuntimeFamily pmdt200to500ms = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_200to500ms");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap200to500ms.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt200to500ms, entry);
        }
        mfs.add(pmdt200to500ms);

        BrokerRuntimeFamily pmdt500to1s = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_500to1s");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap500to1s.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt500to1s, entry);
        }
        mfs.add(pmdt500to1s);

        BrokerRuntimeFamily pmdt1to2s = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_1to2s");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap1to2s.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt1to2s, entry);
        }
        mfs.add(pmdt1to2s);

        BrokerRuntimeFamily pmdt2to3s = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_2to3s");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap2to3s.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt2to3s, entry);
        }
        mfs.add(pmdt2to3s);

        BrokerRuntimeFamily pmdt3to4s = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_3to4s");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap3to4s.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt3to4s, entry);
        }
        mfs.add(pmdt3to4s);

        BrokerRuntimeFamily pmdt4to5s = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_4to5s");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap4to5s.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt4to5s, entry);
        }
        mfs.add(pmdt4to5s);

        BrokerRuntimeFamily pmdt5to10s = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_5to10s");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap5to10s.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt5to10s, entry);
        }
        mfs.add(pmdt5to10s);

        BrokerRuntimeFamily pmdt10stoMore = new BrokerRuntimeFamily("rocketmq_brokeruntime_pmdt_10stomore");
        for (var entry : brokerRuntimePutMessageDistributeTimeMap10toMore.entrySet()) {
            loadBrokerRuntimeStatsMetric(pmdt10stoMore, entry);
        }
        mfs.add(pmdt10stoMore);
    }

    public List<AbstractMetric> toMetrics() {
        List<BrokerRuntimeFamily> mfs = new ArrayList<>();
        collectBrokerRuntimeStatsPutMessageDistributeTime(mfs);

        BrokerRuntimeFamily brokerRuntimeMsgPutTotalTodayNowF = new BrokerRuntimeFamily("rocketmq_brokeruntime_msg_put_total_today_now");
        for (var entry : brokerRuntimeMsgPutTotalTodayNow.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeMsgPutTotalTodayNowF, entry);
        }
        mfs.add(brokerRuntimeMsgPutTotalTodayNowF);

        BrokerRuntimeFamily brokerRuntimeMsgGetTotalTodayNowF = new BrokerRuntimeFamily("rocketmq_brokeruntime_msg_gettotal_today_now");
        for (var entry : brokerRuntimeMsgGetTotalTodayNow.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeMsgGetTotalTodayNowF, entry);
        }
        mfs.add(brokerRuntimeMsgGetTotalTodayNowF);

        BrokerRuntimeFamily brokerRuntimeDispatchBehindBytesF = new BrokerRuntimeFamily("rocketmq_brokeruntime_dispatch_behind_bytes");
        for (var entry : brokerRuntimeDispatchBehindBytes.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeDispatchBehindBytesF, entry);
        }
        mfs.add(brokerRuntimeDispatchBehindBytesF);

        BrokerRuntimeFamily brokerRuntimePutMessageSizeTotalF = new BrokerRuntimeFamily("rocketmq_brokeruntime_put_message_size_total");
        for (var entry : brokerRuntimePutMessageSizeTotal.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePutMessageSizeTotalF, entry);
        }
        mfs.add(brokerRuntimePutMessageSizeTotalF);

        BrokerRuntimeFamily brokerRuntimePutMessageAverageSizeF = new BrokerRuntimeFamily("rocketmq_brokeruntime_put_message_average_size");
        for (var entry : brokerRuntimePutMessageAverageSize.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePutMessageAverageSizeF, entry);
        }
        mfs.add(brokerRuntimePutMessageAverageSizeF);

        BrokerRuntimeFamily brokerRuntimeQueryThreadPoolQueueCapacityF = new BrokerRuntimeFamily("rocketmq_brokeruntime_query_threadpool_queue_capacity");
        for (var entry : brokerRuntimeQueryThreadPoolQueueCapacity.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeQueryThreadPoolQueueCapacityF, entry);
        }
        mfs.add(brokerRuntimeQueryThreadPoolQueueCapacityF);

        BrokerRuntimeFamily brokerRuntimeRemainTransientStoreBufferNumbsF = new BrokerRuntimeFamily("rocketmq_brokeruntime_remain_transientstore_buffer_numbs");
        for (var entry : brokerRuntimeRemainTransientStoreBufferNumbs.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeRemainTransientStoreBufferNumbsF, entry);
        }
        mfs.add(brokerRuntimeRemainTransientStoreBufferNumbsF);

        BrokerRuntimeFamily brokerRuntimeEarliestMessageTimeStampF = new BrokerRuntimeFamily("rocketmq_brokeruntime_earliest_message_timestamp");
        for (var entry : brokerRuntimeEarliestMessageTimeStamp.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeEarliestMessageTimeStampF, entry);
        }
        mfs.add(brokerRuntimeEarliestMessageTimeStampF);

        BrokerRuntimeFamily brokerRuntimePutMessageEntireTimeMaxF = new BrokerRuntimeFamily("rocketmq_brokeruntime_putmessage_entire_time_max");
        for (var entry : brokerRuntimePutMessageEntireTimeMax.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePutMessageEntireTimeMaxF, entry);
        }
        mfs.add(brokerRuntimePutMessageEntireTimeMaxF);

        BrokerRuntimeFamily brokerRuntimeStartAcceptSendRequestTimeStampF = new BrokerRuntimeFamily("rocketmq_brokeruntime_start_accept_sendrequest_time");
        for (var entry : brokerRuntimeStartAcceptSendRequestTimeStamp.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeStartAcceptSendRequestTimeStampF, entry);
        }
        mfs.add(brokerRuntimeStartAcceptSendRequestTimeStampF);

        BrokerRuntimeFamily brokerRuntimeSendThreadPoolQueueSizeF = new BrokerRuntimeFamily("rocketmq_brokeruntime_send_threadpool_queue_size");
        for (var entry : brokerRuntimeSendThreadPoolQueueSize.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeSendThreadPoolQueueSizeF, entry);
        }
        mfs.add(brokerRuntimeSendThreadPoolQueueSizeF);

        BrokerRuntimeFamily brokerRuntimePutMessageTimesTotalF = new BrokerRuntimeFamily("rocketmq_brokeruntime_putmessage_times_total");
        for (var entry : brokerRuntimePutMessageTimesTotal.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePutMessageTimesTotalF, entry);
        }
        mfs.add(brokerRuntimePutMessageTimesTotalF);

        BrokerRuntimeFamily brokerRuntimeGetMessageEntireTimeMaxF = new BrokerRuntimeFamily("rocketmq_brokeruntime_getmessage_entire_time_max");
        for (var entry : brokerRuntimeGetMessageEntireTimeMax.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetMessageEntireTimeMaxF, entry);
        }
        mfs.add(brokerRuntimeGetMessageEntireTimeMaxF);

        BrokerRuntimeFamily brokerRuntimePageCacheLockTimeMillsF = new BrokerRuntimeFamily("rocketmq_brokeruntime_pagecache_lock_time_mills");
        for (var entry : brokerRuntimePageCacheLockTimeMills.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePageCacheLockTimeMillsF, entry);
        }
        mfs.add(brokerRuntimePageCacheLockTimeMillsF);

        BrokerRuntimeFamily brokerRuntimeCommitLogDiskRatioF = new BrokerRuntimeFamily("rocketmq_brokeruntime_commitlog_disk_ratio");
        for (var entry : brokerRuntimeCommitLogDiskRatio.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeCommitLogDiskRatioF, entry);
        }
        mfs.add(brokerRuntimeCommitLogDiskRatioF);

        BrokerRuntimeFamily brokerRuntimeConsumeQueueDiskRatioF = new BrokerRuntimeFamily("rocketmq_brokeruntime_consumequeue_disk_ratio");
        for (var entry : brokerRuntimeConsumeQueueDiskRatio.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeConsumeQueueDiskRatioF, entry);
        }
        mfs.add(brokerRuntimeConsumeQueueDiskRatioF);

        BrokerRuntimeFamily brokerRuntimeGetFoundTps600F = new BrokerRuntimeFamily("rocketmq_brokeruntime_getfound_tps600");
        for (var entry : brokerRuntimeGetFoundTps600.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetFoundTps600F, entry);
        }
        mfs.add(brokerRuntimeGetFoundTps600F);

        BrokerRuntimeFamily brokerRuntimeGetFoundTps60F = new BrokerRuntimeFamily("rocketmq_brokeruntime_getfound_tps60");
        for (var entry : brokerRuntimeGetFoundTps60.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetFoundTps60F, entry);
        }
        mfs.add(brokerRuntimeGetFoundTps60F);

        BrokerRuntimeFamily brokerRuntimeGetFoundTps10F = new BrokerRuntimeFamily("rocketmq_brokeruntime_getfound_tps10");
        for (var entry : brokerRuntimeGetFoundTps10.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetFoundTps10F, entry);
        }
        mfs.add(brokerRuntimeGetFoundTps10F);

        BrokerRuntimeFamily brokerRuntimeGetTotalTps600F = new BrokerRuntimeFamily("rocketmq_brokeruntime_gettotal_tps600");
        for (var entry : brokerRuntimeGetTotalTps600.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetTotalTps600F, entry);
        }
        mfs.add(brokerRuntimeGetTotalTps600F);

        BrokerRuntimeFamily brokerRuntimeGetTotalTps60F = new BrokerRuntimeFamily("rocketmq_brokeruntime_gettotal_tps60");
        for (var entry : brokerRuntimeGetTotalTps60.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetTotalTps60F, entry);
        }
        mfs.add(brokerRuntimeGetTotalTps60F);

        BrokerRuntimeFamily brokerRuntimeGetTotalTps10F = new BrokerRuntimeFamily("rocketmq_brokeruntime_gettotal_tps10");
        for (var entry : brokerRuntimeGetTotalTps10.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetTotalTps10F, entry);
        }
        mfs.add(brokerRuntimeGetTotalTps10F);

        BrokerRuntimeFamily brokerRuntimeGetTransferedTps600F = new BrokerRuntimeFamily("rocketmq_brokeruntime_gettransfered_tps600");
        for (var entry : brokerRuntimeGetTransferedTps600.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetTransferedTps600F, entry);
        }
        mfs.add(brokerRuntimeGetTransferedTps600F);

        BrokerRuntimeFamily brokerRuntimeGetTransferedTps60F = new BrokerRuntimeFamily("rocketmq_brokeruntime_gettransfered_tps60");
        for (var entry : brokerRuntimeGetTransferedTps60.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetTransferedTps60F, entry);
        }
        mfs.add(brokerRuntimeGetTransferedTps60F);

        BrokerRuntimeFamily brokerRuntimeGetTransferedTps10F = new BrokerRuntimeFamily("rocketmq_brokeruntime_gettransfered_tps10");
        for (var entry : brokerRuntimeGetTransferedTps10.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetTransferedTps10F, entry);
        }
        mfs.add(brokerRuntimeGetTransferedTps10F);

        BrokerRuntimeFamily brokerRuntimeGetMissTps600F = new BrokerRuntimeFamily("rocketmq_brokeruntime_getmiss_tps600");
        for (var entry : brokerRuntimeGetMissTps600.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetMissTps600F, entry);
        }
        mfs.add(brokerRuntimeGetMissTps600F);

        BrokerRuntimeFamily brokerRuntimeGetMissTps60F = new BrokerRuntimeFamily("rocketmq_brokeruntime_getmiss_tps60");
        for (var entry : brokerRuntimeGetMissTps60.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetMissTps60F, entry);
        }
        mfs.add(brokerRuntimeGetMissTps60F);

        BrokerRuntimeFamily brokerRuntimeGetMissTps10F = new BrokerRuntimeFamily("rocketmq_brokeruntime_getmiss_tps10");
        for (var entry : brokerRuntimeGetMissTps10.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeGetMissTps10F, entry);
        }
        mfs.add(brokerRuntimeGetMissTps10F);

        BrokerRuntimeFamily brokerRuntimePutTps600F = new BrokerRuntimeFamily("rocketmq_brokeruntime_put_tps600");
        for (var entry : brokerRuntimePutTps600.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePutTps600F, entry);
        }
        mfs.add(brokerRuntimePutTps600F);

        BrokerRuntimeFamily brokerRuntimePutTps60F = new BrokerRuntimeFamily("rocketmq_brokeruntime_put_tps60");
        for (var entry : brokerRuntimePutTps60.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePutTps60F, entry);
        }
        mfs.add(brokerRuntimePutTps60F);

        BrokerRuntimeFamily brokerRuntimePutTps10F = new BrokerRuntimeFamily("rocketmq_brokeruntime_put_tps10");
        for (var entry : brokerRuntimePutTps10.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePutTps10F, entry);
        }
        mfs.add(brokerRuntimePutTps10F);

        BrokerRuntimeFamily brokerRuntimeDispatchMaxBufferF = new BrokerRuntimeFamily("rocketmq_brokeruntime_dispatch_maxbuffer");
        for (var entry : brokerRuntimeDispatchMaxBuffer.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeDispatchMaxBufferF, entry);
        }
        mfs.add(brokerRuntimeDispatchMaxBufferF);

        BrokerRuntimeFamily brokerRuntimePullThreadPoolQueueCapacityF = new BrokerRuntimeFamily("rocketmq_brokeruntime_pull_threadpoolqueue_capacity");
        for (var entry : brokerRuntimePullThreadPoolQueueCapacity.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePullThreadPoolQueueCapacityF, entry);
        }
        mfs.add(brokerRuntimePullThreadPoolQueueCapacityF);

        BrokerRuntimeFamily brokerRuntimeSendThreadPoolQueueCapacityF = new BrokerRuntimeFamily("rocketmq_brokeruntime_send_threadpoolqueue_capacity");
        for (var entry : brokerRuntimeSendThreadPoolQueueCapacity.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeSendThreadPoolQueueCapacityF, entry);
        }
        mfs.add(brokerRuntimeSendThreadPoolQueueCapacityF);

        BrokerRuntimeFamily brokerRuntimePullThreadPoolQueueSizeF = new BrokerRuntimeFamily("rocketmq_brokeruntime_pull_threadpoolqueue_size");
        for (var entry : brokerRuntimePullThreadPoolQueueSize.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePullThreadPoolQueueSizeF, entry);
        }
        mfs.add(brokerRuntimePullThreadPoolQueueSizeF);

        BrokerRuntimeFamily brokerRuntimeQueryThreadPoolQueueSizeF = new BrokerRuntimeFamily("rocketmq_brokeruntime_query_threadpoolqueue_size");
        for (var entry : brokerRuntimeQueryThreadPoolQueueSize.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeQueryThreadPoolQueueSizeF, entry);
        }
        mfs.add(brokerRuntimeQueryThreadPoolQueueSizeF);

        BrokerRuntimeFamily brokerRuntimePullThreadPoolQueueHeadWaitTimeMillsF = new BrokerRuntimeFamily("rocketmq_brokeruntime_pull_threadpoolqueue_headwait_timemills");
        for (var entry : brokerRuntimePullThreadPoolQueueHeadWaitTimeMills.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePullThreadPoolQueueHeadWaitTimeMillsF, entry);
        }
        mfs.add(brokerRuntimePullThreadPoolQueueHeadWaitTimeMillsF);

        BrokerRuntimeFamily brokerRuntimeQueryThreadPoolQueueHeadWaitTimeMillsF = new BrokerRuntimeFamily("rocketmq_brokeruntime_query_threadpoolqueue_headwait_timemills");
        for (var entry : brokerRuntimeQueryThreadPoolQueueHeadWaitTimeMills.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeQueryThreadPoolQueueHeadWaitTimeMillsF, entry);
        }
        mfs.add(brokerRuntimeQueryThreadPoolQueueHeadWaitTimeMillsF);

        BrokerRuntimeFamily brokerRuntimeSendThreadPoolQueueHeadWaitTimeMillsF = new BrokerRuntimeFamily("rocketmq_brokeruntime_send_threadpoolqueue_headwait_timemills");
        for (var entry : brokerRuntimeSendThreadPoolQueueHeadWaitTimeMills.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeSendThreadPoolQueueHeadWaitTimeMillsF, entry);
        }
        mfs.add(brokerRuntimeSendThreadPoolQueueHeadWaitTimeMillsF);

        BrokerRuntimeFamily brokerRuntimeMsgGetTotalYesterdayMorningF = new BrokerRuntimeFamily("rocketmq_brokeruntime_msg_gettotal_yesterdaymorning");
        for (var entry : brokerRuntimeMsgGetTotalYesterdayMorning.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeMsgGetTotalYesterdayMorningF, entry);
        }
        mfs.add(brokerRuntimeMsgGetTotalYesterdayMorningF);

        BrokerRuntimeFamily brokerRuntimeMsgPutTotalYesterdayMorningF = new BrokerRuntimeFamily("rocketmq_brokeruntime_msg_puttotal_yesterdaymorning");
        for (var entry : brokerRuntimeMsgPutTotalYesterdayMorning.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeMsgPutTotalYesterdayMorningF, entry);
        }
        mfs.add(brokerRuntimeMsgPutTotalYesterdayMorningF);

        BrokerRuntimeFamily brokerRuntimeMsgGetTotalTodayMorningF = new BrokerRuntimeFamily("rocketmq_brokeruntime_msg_gettotal_todaymorning");
        for (var entry : brokerRuntimeMsgGetTotalTodayMorning.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeMsgGetTotalTodayMorningF, entry);
        }
        mfs.add(brokerRuntimeMsgGetTotalTodayMorningF);

        BrokerRuntimeFamily brokerRuntimeMsgPutTotalTodayMorningF = new BrokerRuntimeFamily("rocketmq_brokeruntime_msg_puttotal_todaymorning");
        for (var entry : brokerRuntimeMsgPutTotalTodayMorning.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeMsgPutTotalTodayMorningF, entry);
        }
        mfs.add(brokerRuntimeMsgPutTotalTodayMorningF);

        BrokerRuntimeFamily brokerRuntimeCommitLogDirCapacityFreeF = new BrokerRuntimeFamily("rocketmq_brokeruntime_commitlogdir_capacity_free");
        for (var entry : brokerRuntimeCommitLogDirCapacityFree.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeCommitLogDirCapacityFreeF, entry);
        }
        mfs.add(brokerRuntimeCommitLogDirCapacityFreeF);

        BrokerRuntimeFamily brokerRuntimeCommitLogDirCapacityTotalF = new BrokerRuntimeFamily("rocketmq_brokeruntime_commitlogdir_capacity_total");
        for (var entry : brokerRuntimeCommitLogDirCapacityTotal.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeCommitLogDirCapacityTotalF, entry);
        }
        mfs.add(brokerRuntimeCommitLogDirCapacityTotalF);

        BrokerRuntimeFamily brokerRuntimeCommitLogMaxOffsetF = new BrokerRuntimeFamily("rocketmq_brokeruntime_commitlog_maxoffset");
        for (var entry : brokerRuntimeCommitLogMaxOffset.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeCommitLogMaxOffsetF, entry);
        }
        mfs.add(brokerRuntimeCommitLogMaxOffsetF);

        BrokerRuntimeFamily brokerRuntimeCommitLogMinOffsetF = new BrokerRuntimeFamily("rocketmq_brokeruntime_commitlog_minoffset");
        for (var entry : brokerRuntimeCommitLogMinOffset.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeCommitLogMinOffsetF, entry);
        }
        mfs.add(brokerRuntimeCommitLogMinOffsetF);

        BrokerRuntimeFamily brokerRuntimeRemainHowManyDataToFlushF = new BrokerRuntimeFamily("rocketmq_brokeruntime_remain_howmanydata_toflush");
        for (var entry : brokerRuntimeRemainHowManyDataToFlush.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimeRemainHowManyDataToFlushF, entry);
        }
        mfs.add(brokerRuntimeRemainHowManyDataToFlushF);

        BrokerRuntimeFamily brokerRuntimePutLatency99F = new BrokerRuntimeFamily("rocketmq_brokeruntime_put_latency_99");
        for (var entry : brokerRuntimePutLatency99.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePutLatency99F, entry);
        }
        mfs.add(brokerRuntimePutLatency99F);

        BrokerRuntimeFamily brokerRuntimePutLatency999F = new BrokerRuntimeFamily("rocketmq_brokeruntime_put_latency_999");
        for (var entry : brokerRuntimePutLatency999.entrySet()) {
            loadBrokerRuntimeStatsMetric(brokerRuntimePutLatency999F, entry);
        }
        mfs.add(brokerRuntimePutLatency999F);
        List<AbstractMetric> metrics = new ArrayList<>();
        for (BrokerRuntimeFamily family : mfs) {
            metrics.addAll(family.toMetrics());
        }
        return metrics;
    }
}
