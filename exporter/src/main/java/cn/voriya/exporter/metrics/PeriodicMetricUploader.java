package cn.voriya.exporter.metrics;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.thread.SchedulerPool;
import cn.voriya.boot.utils.push.MetricsUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 指标定时收集上报类，会定时收集注册到该类的指标数据，并上报到Prometheus
 */
public class PeriodicMetricUploader {

    private static final long REPORT_INTERVAL_SECONDS = 15; // 15秒

    static {
        SchedulerPool.executeAtNextPeriod(
                PeriodicMetricUploader::report,
                PeriodicMetricUploader.class.getName(),
                REPORT_INTERVAL_SECONDS
        );
    }

    /**
     * 注册的指标数据提供者
     */
    private static final Set<Supplier<List<AbstractMetric>>> registeredMetricSuppliers = ConcurrentHashMap.newKeySet();

    /**
     * 注册指标数据提供者
     *
     * @param supplier 指标数据提供者
     */
    public static void register(Supplier<List<AbstractMetric>> supplier) {
        registeredMetricSuppliers.add(supplier);
    }

    /**
     * 上报指标数据
     */
    private static void report() {
        List<AbstractMetric> metrics = new ArrayList<>();
        for (Supplier<List<AbstractMetric>> supplier : registeredMetricSuppliers) {
            metrics.addAll(supplier.get());
        }
        MetricsUtil.sendMetrics(metrics, "MetricTimerReporter");
    }
}
