/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package cn.voriya.exporter.metrics.rocket;

import lombok.Data;

@Data
public class BrokerRuntimeMetric {
    private String clusterName;
    private Long brokerId;
    private String brokerAddress;
    private String broker;
    private String brokerDes;
    private long bootTimestamp;
    private int brokerVersion;

    public BrokerRuntimeMetric(Long brokerId,
                               String brokerAddress,
                               String broker,
                               String brokerDes,
                               long bootTimestamp,
                               int brokerVersion) {
        this.brokerId = brokerId;
        this.brokerAddress = brokerAddress;
        this.broker = broker;
        this.brokerDes = brokerDes;
        this.bootTimestamp = bootTimestamp;
        this.brokerVersion = brokerVersion;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof BrokerRuntimeMetric other)) {
            return false;
        }

        return other.brokerAddress.equals(brokerAddress);
    }

    @Override
    public int hashCode() {
        int hash = 1;
        hash = 37 * hash + brokerAddress.hashCode();
        return hash;
    }

    @Override
    public String toString() {
        return "BrokerAddress: %s brokerHost: %s".formatted(brokerAddress, broker);
    }
}
