package cn.voriya.exporter.metrics.queue;

import cn.voriya.boot.metrics.AbstractMetric;

public class TopicQueueOffsetMetric extends AbstractMetric {

    public TopicQueueOffsetMetric(String topic, String brokerName, String brokerIdc, Integer queueId, long maxOffset) {
        super("turbo_exporter_topic_queue_offset", maxOffset);
        this.addTag("topic", topic);
        this.addTag("queue", String.valueOf(queueId));
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
    }
}
