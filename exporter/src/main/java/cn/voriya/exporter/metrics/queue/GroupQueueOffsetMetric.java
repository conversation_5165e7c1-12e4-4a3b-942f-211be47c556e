package cn.voriya.exporter.metrics.queue;

import cn.voriya.boot.metrics.AbstractMetric;

public class GroupQueueOffsetMetric extends AbstractMetric {

    public GroupQueueOffsetMetric(String group, String topic, String brokerName, String brokerIdc, Integer queueId, long consumerOffset) {
        super("turbo_exporter_group_queue_offset", consumerOffset);
        this.addTag("group", group);
        this.addTag("topic", topic);
        this.addTag("queue", String.valueOf(queueId));
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
    }
}
