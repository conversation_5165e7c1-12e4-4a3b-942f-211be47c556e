package cn.voriya.exporter.metrics.broker;

import cn.voriya.boot.metrics.AbstractMetric;

public class TopicBrokerOffsetMetric extends AbstractMetric {

    public TopicBrokerOffsetMetric(String topic, String brokerName, String brokerIdc, long maxOffset) {
        super("turbo_exporter_topic_broker_offset", maxOffset);
        this.addTag("topic", topic);
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
    }
}
