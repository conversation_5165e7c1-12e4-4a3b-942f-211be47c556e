package cn.voriya.exporter.metrics.queue;

import cn.voriya.boot.metrics.AbstractMetric;

public class GroupQueueLagMetric extends AbstractMetric {

    public GroupQueueLagMetric(String group, String topic, String brokerName, String brokerIdc, Integer queueId, long diff) {
        super("turbo_exporter_group_queue_lag", diff);
        this.addTag("group", group);
        this.addTag("topic", topic);
        this.addTag("queue", String.valueOf(queueId));
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
    }
}
