package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存命中率指标
 */
public class MemoryCacheHitRateMetric extends AbstractMetric {

    public MemoryCacheHitRateMetric(String cacheName, double hitRate) {
        super("turbo_exporter_memory_cache", hitRate);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "hit_rate");
    }
}
