package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存重建累计总耗时指标（毫秒）
 */
public class MemoryCacheTotalRebuildDurationMetric extends AbstractMetric {

    public MemoryCacheTotalRebuildDurationMetric(String cacheName, long totalRebuildDuration) {
        super("turbo_exporter_memory_cache", totalRebuildDuration);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "total_rebuild_duration_ms");
    }
}
