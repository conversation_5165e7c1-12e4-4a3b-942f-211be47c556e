package cn.voriya.exporter.metrics.broad.client;

import cn.voriya.boot.metrics.AbstractMetric;

public class BroadCastGroupBrokerLagMetric extends AbstractMetric {

    public BroadCastGroupBrokerLagMetric(String group,
                                         String topic,
                                         String brokerName,
                                         String brokerIdc,
                                         String clientId,
                                         long diff,
                                         boolean online) {
        super("turbo_exporter_broad_cast_group_broker_lag", diff);
        this.addTag("group", group);
        this.addTag("topic", topic);
        this.addTag("client_id", clientId);
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
        this.addTag("online", online ? "true" : "false");
    }
}
