package cn.voriya.exporter.metrics.broker;

import cn.voriya.boot.metrics.AbstractMetric;

public class GroupBrokerLagMetric extends AbstractMetric {

    public GroupBrokerLagMetric(String group, String topic, String brokerName, String brokerIdc, long diff) {
        super("turbo_exporter_group_broker_lag", diff);
        this.addTag("group", group);
        this.addTag("topic", topic);
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
    }
}
