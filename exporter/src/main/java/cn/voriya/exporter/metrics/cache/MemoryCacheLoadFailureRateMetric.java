package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存加载失败率指标
 */
public class MemoryCacheLoadFailureRateMetric extends AbstractMetric {
    
    public MemoryCacheLoadFailureRateMetric(String cacheName, double loadFailureRate) {
        super("turbo_exporter_memory_cache", loadFailureRate);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "load_failure_rate");
    }
}
