package cn.voriya.exporter.metrics.time;

import cn.voriya.exporter.entity.enums.CollectionTaskType;
import cn.voriya.boot.metrics.AbstractMetric;

public class TaskCollectCompleteTimeMetric extends AbstractMetric {

    public TaskCollectCompleteTimeMetric(CollectionTaskType taskType, String taskId, long time) {
        super("turbo_exporter_task_collect_complete_time", time);
        this.addTag("task_type", taskType.name().toLowerCase());
        this.addTag("task_id", taskId);
    }
}
