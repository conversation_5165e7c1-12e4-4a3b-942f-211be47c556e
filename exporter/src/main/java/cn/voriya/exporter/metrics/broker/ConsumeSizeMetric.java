package cn.voriya.exporter.metrics.broker;

import cn.voriya.boot.metrics.AbstractMetric;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;

public class ConsumeSizeMetric extends AbstractMetric {
    public ConsumeSizeMetric(String group, String topic, String brokerName, String brokerIdc, MessageModel messageModel, String consumeType, long size) {
        super("turbo_exporter_group_size", size);
        this.addTag("group", group);
        this.addTag("topic", topic);
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
        this.addTag("messageModel", messageModel.name());
        this.addTag("consumeType", consumeType);
    }
}
