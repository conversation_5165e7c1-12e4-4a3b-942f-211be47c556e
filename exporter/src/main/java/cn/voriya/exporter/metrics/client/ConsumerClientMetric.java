package cn.voriya.exporter.metrics.client;

import cn.voriya.boot.metrics.AbstractMetric;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.alibaba.rocketmq.remoting.protocol.LanguageCode;

public class ConsumerClientMetric extends AbstractMetric {

    public ConsumerClientMetric(String group,
                                String brokerName,
                                String addr,
                                MessageModel messageModel,
                                String clientId,
                                String clientAddr,
                                LanguageCode language,
                                int version) {
        super("turbomq_exporter_consumer_client", 1);
        this.addTag("group", group);
        this.addTag("broker", brokerName);
        this.addTag("brokerIP", addr);
        this.addTag("messageModel", messageModel.name());
        this.addTag("clientId", clientId);
        this.addTag("clientAddr", clientAddr);
        this.addTag("language", language == null ? "-" : language.name());
        this.addTag("version", String.valueOf(version));
    }
}
