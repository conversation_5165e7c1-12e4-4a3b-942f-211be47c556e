package cn.voriya.exporter.metrics.broker;

import cn.voriya.boot.metrics.AbstractMetric;

public class GroupBrokerOffsetMetric extends AbstractMetric {

    public GroupBrokerOffsetMetric(String group, String topic, String brokerName, String brokerIdc, long consumerOffset) {
        super("turbo_exporter_group_broker_offset", consumerOffset);
        this.addTag("group", group);
        this.addTag("topic", topic);
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
    }
}
