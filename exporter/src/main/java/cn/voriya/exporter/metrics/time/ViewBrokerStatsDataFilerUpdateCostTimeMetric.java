package cn.voriya.exporter.metrics.time;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;
import cn.voriya.exporter.entity.enums.CollectionTaskType;

public class ViewBrokerStatsDataFilerUpdateCostTimeMetric extends AbstractMetric {
    public ViewBrokerStatsDataFilerUpdateCostTimeMetric(CollectionTaskType taskType, double time) {
        super("turbo_exporter_view_broker_stats_data_filer_update_cost_time", time);
        this.addTag("task_type", taskType.name().toLowerCase());
        this.addTag("ident", IdUtil.getClientId());
    }
}
