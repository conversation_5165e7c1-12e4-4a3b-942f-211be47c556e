package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存平均加载时间指标（纳秒）
 */
public class MemoryCacheAverageLoadTimeMetric extends AbstractMetric {

    public MemoryCacheAverageLoadTimeMetric(String cacheName, double averageLoadTimeNanos) {
        super("turbo_exporter_memory_cache", averageLoadTimeNanos);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "average_load_time_nanos");
    }
}
