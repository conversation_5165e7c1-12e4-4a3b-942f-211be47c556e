package cn.voriya.exporter.metrics.probe;

import cn.voriya.boot.metrics.AbstractMetric;

public class ProbeProduceCostMetric extends AbstractMetric {

    public ProbeProduceCostMetric(String node, String az, String brokerName, double cost, long timestamp) {
        super("turbo_exporter_probe_produce_cost", cost);
        this.setTimestamp(timestamp);
        this.addTag("node", node);
        this.addTag("az", az);
        this.addTag("broker", brokerName);
    }
}
