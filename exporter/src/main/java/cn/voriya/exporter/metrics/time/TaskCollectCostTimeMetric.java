package cn.voriya.exporter.metrics.time;

import cn.voriya.exporter.entity.enums.CollectionTaskType;
import cn.voriya.boot.metrics.AbstractMetric;

public class TaskCollectCostTimeMetric extends AbstractMetric {

    public TaskCollectCostTimeMetric(CollectionTaskType taskType, String taskId, long time) {
        super("turbo_exporter_task_collect_cost_time", time);
        this.addTag("task_id", taskId);
        this.addTag("task_type", taskType.name().toLowerCase());
    }
}
