package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存总加载时间指标（纳秒）
 */
public class MemoryCacheTotalLoadTimeMetric extends AbstractMetric {
    
    public MemoryCacheTotalLoadTimeMetric(String cacheName, long totalLoadTime) {
        super("turbo_exporter_memory_cache", totalLoadTime);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "total_load_time_nanos");
    }
}
