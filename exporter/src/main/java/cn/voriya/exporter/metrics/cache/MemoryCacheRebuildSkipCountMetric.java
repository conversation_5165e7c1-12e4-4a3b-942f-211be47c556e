package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存重建跳过次数指标
 */
public class MemoryCacheRebuildSkipCountMetric extends AbstractMetric {
    
    public MemoryCacheRebuildSkipCountMetric(String cacheName, long rebuildSkipCount) {
        super("turbo_exporter_memory_cache", rebuildSkipCount);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "rebuild_skip_count");
    }
}
