package cn.voriya.exporter.metrics.rocket;

import cn.voriya.boot.metrics.AbstractMetric;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;

public class ConsumeSizeTPSMetric extends AbstractMetric {
    public ConsumeSizeTPSMetric(String group, String topic, String brokerName, String brokerIdc, MessageModel messageModel, String consumeType, double tps) {
        super("rocketmq_consumer_message_size", tps);
        this.addTag("group", group);
        this.addTag("topic", topic);
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
        this.addTag("messageModel", messageModel.name());
        this.addTag("consumeType", consumeType);
    }
}
