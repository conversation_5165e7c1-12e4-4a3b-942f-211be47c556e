package cn.voriya.exporter.metrics.broad.client;

import cn.voriya.boot.metrics.AbstractMetric;

public class BroadCastGroupBrokerOffsetMetric extends AbstractMetric {

    public BroadCastGroupBrokerOffsetMetric(String group,
                                            String topic,
                                            String brokerName,
                                            String brokerIdc,
                                            String clientId,
                                            long consumerOffset,
                                            boolean online) {
        super("turbo_exporter_broad_cast_group_broker_offset", consumerOffset);
        this.addTag("group", group);
        this.addTag("topic", topic);
        this.addTag("client_id", clientId);
        this.addTag("broker", brokerName);
        this.addTag("brokerIdc", brokerIdc);
        this.addTag("online", online ? "true" : "false");
    }
}
