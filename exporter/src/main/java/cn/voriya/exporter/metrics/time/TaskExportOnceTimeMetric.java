package cn.voriya.exporter.metrics.time;

import cn.voriya.boot.utils.IdUtil;
import cn.voriya.exporter.entity.enums.CollectionTaskType;
import cn.voriya.boot.metrics.AbstractMetric;

public class TaskExportOnceTimeMetric extends AbstractMetric {
    public TaskExportOnceTimeMetric(CollectionTaskType taskType, double time) {
        super("turbo_exporter_task_once_time", time);
        this.addTag("task_type", taskType.name().toLowerCase());
        this.addTag("ident", IdUtil.getClientId());
    }
}
