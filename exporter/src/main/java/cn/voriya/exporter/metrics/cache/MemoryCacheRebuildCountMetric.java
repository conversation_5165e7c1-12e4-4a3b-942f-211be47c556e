package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存重建次数指标
 */
public class MemoryCacheRebuildCountMetric extends AbstractMetric {
    
    public MemoryCacheRebuildCountMetric(String cacheName, long rebuildCount) {
        super("turbo_exporter_memory_cache", rebuildCount);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "rebuild_count");
    }
}
