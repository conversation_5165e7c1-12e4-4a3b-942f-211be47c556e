package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存大小指标
 */
public class MemoryCacheSizeMetric extends AbstractMetric {

    public MemoryCacheSizeMetric(String cacheName, long cacheSize) {
        super("turbo_exporter_memory_cache", cacheSize);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "size");
    }
}
