package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存驱逐次数指标
 */
public class MemoryCacheEvictionCountMetric extends AbstractMetric {

    public MemoryCacheEvictionCountMetric(String cacheName, long evictionCount) {
        super("turbo_exporter_memory_cache", evictionCount);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "eviction_count");
    }
}
