package cn.voriya.exporter.metrics.probe;

import cn.voriya.boot.metrics.AbstractMetric;

public class ProbeConsumeCostMetric extends AbstractMetric {

    public ProbeConsumeCostMetric(String node,
                                  String az,
                                  String brokerName,
                                  String startFrom,
                                  double cost,
                                  long timestamp) {
        super("turbo_exporter_probe_consume_cost", cost);
        this.setTimestamp(timestamp);
        this.addTag("node", node);
        this.addTag("az", az);
        this.addTag("broker", brokerName);
        this.addTag("start_from", startFrom);
    }
}
