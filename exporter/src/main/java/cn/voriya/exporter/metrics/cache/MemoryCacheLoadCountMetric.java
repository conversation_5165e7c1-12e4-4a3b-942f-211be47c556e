package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存加载次数指标
 */
public class MemoryCacheLoadCountMetric extends AbstractMetric {

    public MemoryCacheLoadCountMetric(String cacheName, long loadCount) {
        super("turbo_exporter_memory_cache", loadCount);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "load_count");
    }
}
