package cn.voriya.exporter.metrics.cache;

import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.boot.utils.IdUtil;

/**
 * MemoryCache 缓存访问次数指标
 */
public class MemoryCacheRequestCountMetric extends AbstractMetric {

    public MemoryCacheRequestCountMetric(String cacheName, long requestCount) {
        super("turbo_exporter_memory_cache", requestCount);
        this.addTag("ident", IdUtil.getClientId());
        this.addTag("cache_name", cacheName);
        this.addTag("metric_type", "request_count");
    }
}
