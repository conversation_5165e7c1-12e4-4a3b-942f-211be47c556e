env: "${DAOKEENVTYPE:-dev}"
uk: "${DAOKEAPPUK:-tcbase.ke.hd}"
idc: "${DAOKE_LOGIC_IDC:-officeidc_hd1}"
idcToAz:
  officeidc_hd1: "hd1"
  officeidc_hd2: "hd1"
  logicidc_hd1: "hd1"
  logicidc_hd2: "hd2"
  logicidc_hd3: "hd1"
  logicidc_idc1: "hd1"
  logicidc_idc2: "hd2"
  logicidc_idc3: "hd1"
  idc1: "hb1"
  idc2: "hb2"
  logicidc_sg1: "sg1"
  logicidc_sg2: "sg2"

profiles:
  dev:
    vmEndpoints:
      default:
        - url: "http://infprometheus.dss.qa.17usoft.com/write/api/v1/push"
          headers:
            X-Scope-OrgID: "default"
  qa:
    vmEndpoints:
      default:
        - url: "http://infprometheus.dss.qa.17usoft.com/write/api/v1/push"
          headers:
            X-Scope-OrgID: "default"
  product:
    vmEndpoints:
      logicidc_hd1:
        - url: "http://hd1.infprometheus.dss.17usoft.com/write1/api/v1/push"
          headers:
            X-Scope-OrgID: "default"
      logicidc_hd2:
        - url: "http://hd2.infprometheus.dss.17usoft.com/write1/api/v1/push"
          headers:
            X-Scope-OrgID: "default"
      idc1:
        - url: "http://hb1.infprometheus.dss.17usoft.com/write1/api/v1/push"
          headers:
            X-Scope-OrgID: "default"
      idc2:
        - url: "http://hb2.infprometheus.dss.17usoft.com/write1/api/v1/push"
          headers:
            X-Scope-OrgID: "default"
      logicidc_sg1:
        - url: "http://infprometheus.dss.17usoft.com/write1/api/v1/push"
          headers:
            X-Scope-OrgID: "default"
        - url: "http://ap-southeast-1.infprometheus.dss.17usoft.com/write1/api/v1/push"
          headers:
            X-Scope-OrgID: "default"
      logicidc_sg2:
        - url: "http://infprometheus.dss.17usoft.com/write1/api/v1/push"
          headers:
            X-Scope-OrgID: "default"
        - url: "http://ap-southeast-1.infprometheus.dss.17usoft.com/write1/api/v1/push"
          headers:
            X-Scope-OrgID: "default"