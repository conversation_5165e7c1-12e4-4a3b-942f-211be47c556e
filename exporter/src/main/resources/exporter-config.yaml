env: "${DAOKEENVTYPE:-dev}"
uk: "${DAOKEAPPUK:-tcbase.ke.hd}"
idc: "${DAOKE_LOGIC_IDC:-officeidc_hd1}"
port: 4399
activeProbing:
  topic: "turbomq_probe_topic"
  tag: "turbomq_exporter_probe_tag"
  producer: "turbomq_probe_producer"
  consumer: "turbomq_probe_consumer_group"
nameserver: "${NAMESERVER:-}"
clusterId: "${CLUSTER_ID:-}"
kafkaConfig:
  servers:
    default: "kafka.ops.17usoft.com:9092"
  topic: "tcbase_mqs_druid_topic_qa"

profiles:
  dev:
    nameserver: "10.178.66.155:9888"
    clusterId: "8"
  product:
    kafkaConfig:
      servers:
        default: "sz.kafka.dss.17usoft.com:9092"
        idc1: "bj.kafka.dss.17usoft.com:9092"
        idc2: "bj.kafka.dss.17usoft.com:9092"
      topic: "tcbase_mqs_druid_topic_product"