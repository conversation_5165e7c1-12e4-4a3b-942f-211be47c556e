<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="CONSOLE_INFO_LOG_PATTERN"
              value="%green([%-5level][%-30thread]) %blue([%date{yyyy-MM-dd HH:mm:ss}]) %cyan(%logger{36})::%blue(%M\(%L\)) %msg%n"/>
    <property name="CONSOLE_WARN_LOG_PATTERN"
              value="%yellow([%-5level][%-30thread]) %blue([%date{yyyy-MM-dd HH:mm:ss}]) %cyan(%logger{36})::%blue(%M\(%L\)) %yellow(%msg%n)"/>
    <property name="CONSOLE_ERROR_LOG_PATTERN"
              value="%red([%-5level][%-30thread]) %blue([%date{yyyy-MM-dd HH:mm:ss}]) %cyan(%logger{36})::%blue(%M\(%L\)) %red(%msg%n)"/>
    <property name="CONSOLE_OTHER_LOG_PATTERN"
              value="%highlight([%-5level][%-30thread]) %blue([%date{yyyy-MM-dd HH:mm:ss}]) %cyan(%logger{36})::%blue(%M\(%L\)) %msg%n"/>
    <property name="FILE_LOG_PATTERN" value="%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level &lt;%logger{36}&gt;&lt;%M&gt;&lt;%L&gt; | %msg%n"/>
    <property name="LOG_PATH" value="${LOG_FILE_PATH:-./logs}"/>
    <property name="LOG_LEVEL" value="${APP_LOG_LEVEL:-INFO}"/>

    <!-- Console Appender -->
    <appender name="ConsoleInfo" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_INFO_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="ConsoleWarn" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_WARN_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="ConsoleError" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_ERROR_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="ConsoleOther" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_OTHER_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>TRACE</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>


    <!-- RollingFile Appender -->
    <appender name="File" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/app.log.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>2</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    <appender name="AsyncConsoleInfo" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ConsoleInfo"/>
        <queueSize>500</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>
    <appender name="AsyncConsoleWarn" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ConsoleWarn"/>
        <queueSize>500</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>
    <appender name="AsyncConsoleError" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ConsoleError"/>
        <queueSize>500</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>
    <appender name="AsyncConsoleOther" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ConsoleOther"/>
        <queueSize>500</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>
    <appender name="AsyncFile" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="File"/>
        <queueSize>5000</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!-- Root Logger -->
    <root level="${LOG_LEVEL}">
        <appender-ref ref="AsyncConsoleInfo"/>
        <appender-ref ref="AsyncConsoleWarn"/>
        <appender-ref ref="AsyncConsoleError"/>
        <appender-ref ref="AsyncConsoleOther"/>
        <appender-ref ref="AsyncFile"/>
    </root>
</configuration>
