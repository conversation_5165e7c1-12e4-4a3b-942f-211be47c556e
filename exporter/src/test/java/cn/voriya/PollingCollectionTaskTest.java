package cn.voriya;

import cn.voriya.exporter.client.TurboClient;
import cn.voriya.exporter.config.ExporterConfig;
import cn.voriya.exporter.tasks.PollingCollectionTask;
import org.junit.jupiter.api.Test;

public class PollingCollectionTaskTest {
    @Test
    public void collectBroadCastGroupMetricsTest(){
        TurboClient turboClient = new TurboClient(ExporterConfig.getInstance().getNameserver());
        PollingCollectionTask pollingCollectionTask = new PollingCollectionTask();
        pollingCollectionTask.collectBroadCastGroupMetrics();
    }
}
