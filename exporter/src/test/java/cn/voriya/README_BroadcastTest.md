# 广播模式MQ测试说明

## 概述
这是一个RocketMQ广播模式的测试套件，用于测试生产者和消费者在广播模式下的消息收发功能。

## 配置信息
- **NameServer**: `mqnameserver.uat.17usoft.com:9876`
- **Topic**: `test_topic_broadcast_1000msg`
- **Producer Group**: `test_producer_group_1000msg`
- **Consumer Group**: `test_broadcast_group_1000msg`
- **消息发送频率**: 1000条/分钟 (约每60毫秒发送1条)
- **消费模式**: 广播模式 (BROADCASTING)

## 文件说明

### 1. MQResourceManager.java
MQ资源管理器，用于创建和管理Topic、Consumer Group等资源。

**主要功能**:
- 检查Topic是否存在
- 创建Topic（默认8个队列，读写权限）
- 删除Topic
- 打印Topic信息
- 获取所有Topic列表

### 2. BroadcastMQTest.java
JUnit测试类，包含完整的测试方法。

**测试方法**:
- `initMQResources()`: 初始化MQ资源，创建Topic
- `startProducer()`: 启动生产者，每分钟发送1000条消息
- `startBroadcastConsumer()`: 启动广播消费者
- `simpleProducerTest()`: 简单生产者测试，发送10条消息
- `simpleBroadcastConsumerTest()`: 简单消费者测试

### 3. BroadcastProducer.java
独立的生产者程序，可以直接运行。

**特性**:
- 自动检查并创建Topic
- 每分钟发送1000条消息
- 实时统计发送成功率
- 优雅关闭机制

### 4. BroadcastConsumer.java
独立的消费者程序，可以直接运行。

**特性**:
- 广播模式消费
- 自动检查Topic存在性
- 实时统计消费情况
- 优雅关闭机制

## 使用步骤

### 方式一：使用JUnit测试
1. **初始化资源**
   ```bash
   # 运行测试方法创建Topic
   mvn test -Dtest=BroadcastMQTest#initMQResources
   ```

2. **简单测试**
   ```bash
   # 发送10条测试消息
   mvn test -Dtest=BroadcastMQTest#simpleProducerTest
   
   # 启动简单消费者测试
   mvn test -Dtest=BroadcastMQTest#simpleBroadcastConsumerTest
   ```

3. **完整测试**
   ```bash
   # 启动生产者（运行10分钟）
   mvn test -Dtest=BroadcastMQTest#startProducer
   
   # 启动消费者（运行10分钟）
   mvn test -Dtest=BroadcastMQTest#startBroadcastConsumer
   ```

### 方式二：直接运行独立程序
1. **启动生产者**
   ```bash
   # 编译并运行生产者
   mvn compile exec:java -Dexec.mainClass="cn.voriya.BroadcastProducer"
   ```

2. **启动消费者**
   ```bash
   # 编译并运行消费者（在另一个终端）
   mvn compile exec:java -Dexec.mainClass="cn.voriya.BroadcastConsumer"
   ```

## 监控和验证

### 生产者监控
- 每100条消息打印一次发送日志
- 每分钟统计发送情况
- 显示成功率和发送速率

### 消费者监控
- 每100条消息打印一次消费日志
- 每分钟统计消费情况
- 显示消费速率和成功率

### 广播模式验证
1. 启动多个消费者实例
2. 每个消费者都应该收到所有消息
3. 消息不会在消费者之间负载均衡

## 注意事项

1. **Topic创建**: 首次运行前需要创建Topic，可以运行`initMQResources()`或直接运行生产者（会自动创建）

2. **网络连接**: 确保能够访问NameServer地址 `mqnameserver.uat.17usoft.com:9876`

3. **资源清理**: 测试完成后可以删除测试Topic：
   ```java
   MQResourceManager resourceManager = new MQResourceManager(NAME_SERVER);
   resourceManager.start();
   resourceManager.deleteTopic("test_topic_broadcast_1000msg");
   resourceManager.shutdown();
   ```

4. **并发测试**: 可以同时启动多个消费者实例来验证广播模式

5. **性能调优**: 如需调整发送频率，修改`MESSAGES_PER_MINUTE`常量和对应的调度间隔

## 故障排除

### 常见问题
1. **Topic不存在**: 运行`initMQResources()`创建Topic
2. **连接失败**: 检查NameServer地址和网络连接
3. **权限问题**: 确保有创建Topic的权限
4. **消息堆积**: 检查消费者是否正常运行

### 日志级别
建议设置日志级别为INFO以查看详细的运行信息。

## 扩展功能
- 可以修改消息内容格式
- 可以添加消息属性和标签
- 可以调整队列数量和权限
- 可以添加消息过滤功能
