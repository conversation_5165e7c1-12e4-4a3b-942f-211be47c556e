package cn.voriya;

import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.DefaultMQProducer;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 广播模式生产者
 * 每分钟发送1000条消息到指定topic
 */
@Slf4j
public class BroadcastProducer {

    private static final String NAME_SERVER = "mqnameserver.uat.17usoft.com:9876";
    private static final String TOPIC = "test_topic_broadcast_1000msg";
    private static final String PRODUCER_GROUP = "test_producer_group_1000msg";
    private static final String CONSUMER_GROUP = "test_broadcast_group_1000msg";
    private static final int MESSAGES_PER_MINUTE = 1000;

    public static void main(String[] args) throws Exception {
        // 首先检查并创建必要的MQ资源
        MQResourceManager resourceManager = new MQResourceManager(NAME_SERVER);
        try {
            resourceManager.start();
            log.info("检查Topic是否存在...");

            if (!resourceManager.ensureTopicExists(TOPIC)) {
                log.error("Topic {} 创建失败，无法启动生产者", TOPIC);
                return;
            }

            // 同时创建消费组（广播模式），方便后续消费者使用
            boolean groupReady = resourceManager.ensureConsumerGroupExists(CONSUMER_GROUP, true);
            if (groupReady) {
                log.info("消费组 {} 创建/确认成功（广播模式）", CONSUMER_GROUP);
            } else {
                log.warn("消费组 {} 创建失败，但不影响生产者运行", CONSUMER_GROUP);
            }

            resourceManager.printTopicInfo(TOPIC);
        } finally {
            resourceManager.shutdown();
        }

        DefaultMQProducer producer = new DefaultMQProducer(PRODUCER_GROUP);
        producer.setNamesrvAddr(NAME_SERVER);
        producer.setInstanceName("BroadcastProducer_" + System.currentTimeMillis());

        try {
            producer.start();
            log.info("=== 广播模式生产者启动成功 ===");
            log.info("NameServer: {}", NAME_SERVER);
            log.info("Topic: {}", TOPIC);
            log.info("Producer Group: {}", PRODUCER_GROUP);
            log.info("发送频率: {}条/分钟 (约每{}毫秒发送1条)", MESSAGES_PER_MINUTE, 60000 / MESSAGES_PER_MINUTE);
            log.info("================================");
            
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
            AtomicLong messageCount = new AtomicLong(0);
            AtomicLong successCount = new AtomicLong(0);
            AtomicLong failureCount = new AtomicLong(0);
            
            // 每60毫秒发送一条消息，实现每分钟1000条的目标
            scheduler.scheduleAtFixedRate(() -> {
                try {
                    long msgId = messageCount.incrementAndGet();
                    String messageBody = String.format(
                        "广播测试消息 #%d - 时间戳: %d - 线程: %s", 
                        msgId, System.currentTimeMillis(), Thread.currentThread().getName()
                    );
                    
                    Message message = new Message(TOPIC, "BroadcastTag", messageBody.getBytes());
                    message.putUserProperty("messageId", String.valueOf(msgId));
                    message.putUserProperty("sendTime", String.valueOf(System.currentTimeMillis()));
                    
                    SendResult sendResult = producer.send(message);
                    successCount.incrementAndGet();
                    
                    if (msgId % 100 == 0) { // 每100条消息打印一次日志
                        log.info("已发送消息 #{}, 发送结果: {}, MsgId: {}", 
                                msgId, sendResult.getSendStatus(), sendResult.getMsgId());
                    }
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    log.error("发送消息失败", e);
                }
            }, 0, 60, TimeUnit.MILLISECONDS);
            
            // 每分钟统计发送情况
            scheduler.scheduleAtFixedRate(() -> {
                long total = messageCount.get();
                long success = successCount.get();
                long failure = failureCount.get();
                
                log.info("=== 发送统计 ===");
                log.info("总发送: {} 条", total);
                log.info("成功: {} 条", success);
                log.info("失败: {} 条", failure);
                log.info("成功率: {:.2f}%", success * 100.0 / total);
                log.info("当前发送速率: {:.1f} 条/分钟", total / ((System.currentTimeMillis() - startTime) / 60000.0));
                log.info("===============");
            }, 1, 1, TimeUnit.MINUTES);
            
            // 添加优雅关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("正在关闭生产者...");
                scheduler.shutdown();
                try {
                    if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                        scheduler.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    scheduler.shutdownNow();
                }
                producer.shutdown();
                log.info("生产者已关闭，总共发送消息: {} 条", messageCount.get());
            }));
            
            // 保持运行状态
            log.info("生产者正在运行，按 Ctrl+C 停止...");
            Thread.currentThread().join();
            
        } catch (MQClientException e) {
            log.error("生产者启动失败", e);
            throw e;
        }
    }
    
    private static final long startTime = System.currentTimeMillis();
}
