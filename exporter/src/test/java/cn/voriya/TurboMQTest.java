package cn.voriya;

import cn.voriya.boot.VoriyaBoot;
import cn.voriya.boot.metrics.AbstractMetric;
import cn.voriya.exporter.cache.impl.TCBaseClient;
import cn.voriya.exporter.client.TurboClient;
import cn.voriya.exporter.config.ExporterConfig;
import cn.voriya.exporter.entity.TaskInfo;
import cn.voriya.exporter.entity.dto.TopicResult;
import cn.voriya.exporter.rocket.RMQMetricsCollector;
import cn.voriya.exporter.service.MetricService;
import cn.voriya.exporter.utils.TurboMQUtil;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.impl.MQClientManager;
import com.alibaba.rocketmq.client.impl.factory.MQClientInstance;
import com.alibaba.rocketmq.common.MixAll;
import com.alibaba.rocketmq.common.admin.ConsumeStats;
import com.alibaba.rocketmq.common.protocol.body.ConsumerConnection;
import com.alibaba.rocketmq.common.protocol.body.KVTable;
import com.alibaba.rocketmq.common.protocol.body.TopicConfigSerializeWrapper;
import com.alibaba.rocketmq.common.protocol.route.BrokerData;
import com.alibaba.rocketmq.common.protocol.route.TopicRouteData;
import com.alibaba.rocketmq.remoting.exception.RemotingConnectException;
import com.alibaba.rocketmq.remoting.exception.RemotingSendRequestException;
import com.alibaba.rocketmq.remoting.exception.RemotingTimeoutException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
@Slf4j
public class TurboMQTest {
    TurboClient turboClient = new TurboClient(ExporterConfig.getInstance().getNameserver());

    @SneakyThrows
    @Test
    public void test() {
        String group = "ccc";
        TopicRouteData topicRouteData = turboClient.getAdmin().examineTopicRouteInfo(MixAll.getRetryTopic(group));
        MQClientInstance mqClient = MQClientManager.getInstance().getAndCreateMQClientInstance(turboClient.getAdmin());
        List<BrokerData> brokerDatas = topicRouteData.getBrokerDatas();
        ConsumeStats result = new ConsumeStats();
        for (BrokerData bd : brokerDatas) {
            String addr = bd.selectBrokerAddr();
            if (addr != null) {
                // 由于查询时间戳会产生IO操作，可能会耗时较长，所以超时时间设置为15s
                ConsumeStats consumeStats =
                        mqClient.getMQClientAPIImpl().getConsumeStats(addr, group, null, 15000);
                result.getOffsetTable().putAll(consumeStats.getOffsetTable());
                long value = result.getConsumeTps() + consumeStats.getConsumeTps();
                result.setConsumeTps(value);
            }
        }
        System.out.println(topicRouteData);
        System.out.println(result);
    }

    @Test
    public void getAllTopicConfigTest() {
        for (BrokerData brokerData : turboClient.getClusterInfo().getBrokerAddrTable().values()) {
            String addr = brokerData.selectBrokerAddr();
            if(addr == null) {
                continue;
            }
            try {
                TopicConfigSerializeWrapper topicConfigSerializeWrapper = turboClient.getAdmin().getAllTopicConfigByAddr(addr);
                System.out.println(topicConfigSerializeWrapper);
            } catch (MQBrokerException | InterruptedException | RemotingTimeoutException |
                     RemotingSendRequestException | RemotingConnectException | IOException e) {
                log.error("获取topic配置失败", e);
            }
        }
    }
    @Test
    public void getTopicRouteTest(){
        String topic = "test";
        try {
            TopicRouteData topicRouteData = turboClient.getAdmin().examineTopicRouteInfo(topic);
            System.out.println(topicRouteData);
        } catch (Exception e) {
            log.error("获取topic路由信息失败", e);
        }
    }
    @Test
    public void getTopicProduceQueueInfoTest(){
        TaskInfo<TopicResult> info;
        try (TurboClient client = new TurboClient("mqnameserver.qa.17usoft.com:9877")) {
            info = client.getTopicProduceQueueInfo("RMQ_SYS_TRANS_HALF_TOPIC");
        }
        List<AbstractMetric> metrics = MetricService.makeTopicMetrics(List.of(info));
        System.out.println(info);
    }

    @Test
    public void fetchBrokerRuntimeStatsTest() throws Exception {
        try (TurboClient client = new TurboClient("mqnameserver.qa.17usoft.com:9876")) {
            for (BrokerData brokerData : client.getClusterInfo().getBrokerAddrTable().values()) {
                String addr = brokerData.selectBrokerAddr();
                KVTable table = client.getAdmin().fetchBrokerRuntimeStats(addr);
                RMQMetricsCollector rmqMetricsCollector = new RMQMetricsCollector();
                rmqMetricsCollector.addBrokerRuntimeStatsMetric(table, 0L, addr, brokerData.getBrokerName());
                List<AbstractMetric> metrics = rmqMetricsCollector.toMetrics();
                System.out.println(metrics);
            }
        }
    }

    @Test
    public void examineAllBrokerConsumerConnectionInfoTest() throws Exception {
        try (TurboClient client = new TurboClient("mqnameserver.qa.17usoft.com:9876")) {
            Map<BrokerData, ConsumerConnection> connectionMap = TurboMQUtil.examineAllBrokerConsumerConnectionInfo(
                    client,
                    TurboMQUtil.getAddrFunc(),
                    "mqs_datasync_group_qa_TCNB"
            );
            System.out.println(connectionMap);
        }
    }

    @Test
    public void getBrokersIdcTest() {
        VoriyaBoot.initialize(TCBaseClient.getInstance(), "cn.voriya");
        try (TurboClient client = new TurboClient("mqnameserver.qa.17usoft.com:9876")) {
            Set<String> brokersIdc = client.getClusterBrokerAzs();
            System.out.println(brokersIdc);
        }
    }

}

