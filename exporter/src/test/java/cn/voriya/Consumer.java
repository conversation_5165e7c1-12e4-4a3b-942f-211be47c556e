package cn.voriya;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.common.consumer.ConsumeFromWhere;
import lombok.extern.slf4j.Slf4j;


/**
 * Consumer，订阅消息
 */
@Slf4j
public class Consumer {

    /**
     * 设置Consumer第一次启动是从队列头部开始消费还是队列尾部开始消费<br>
     * 如果非第一次启动，那么按照上次消费的位置继续消费
     */
    public static void main(String[] args) throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer("fjj_test_group_0612_01");//填写对应的消费组
        consumer.setNamesrvAddr("mqnameserver.qa.17usoft.com:9876");//NameServer地址
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);//设置your_conumer_group第一次上线时，从哪里开始消费消息。例子是从Topic最开始的offset开始读。注意：设置成CONSUME_FROM_LAST_OFFSET，则第一次上线前的消息都不会消费。这个参数只对该group第一次上线时有效，以后上线时，都会从上次消费的位置开始消费。

        consumer.subscribe("20201216_wlltest_topic", "*");//填写订阅的主题以及对应的Tag

        consumer.registerMessageListener((MessageListenerConcurrently) (messages, _) -> {
            System.out.println(STR."\{Thread.currentThread().getName()} Receive New Messages: \{messages}");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;//消费成功返回的ACK
        });

        try {
            consumer.start();   //消费组启动
        } catch (MQClientException e) {
            log.error("consumer start error", e);
        }

        System.out.println("Consumer Started.");
    }
}