package cn.voriya;

import com.alibaba.rocketmq.common.TopicConfig;
import com.alibaba.rocketmq.common.protocol.route.BrokerData;
import com.alibaba.rocketmq.common.protocol.route.TopicRouteData;
import com.alibaba.rocketmq.tools.admin.DefaultMQAdminExt;
import lombok.extern.slf4j.Slf4j;

/**
 * MQ资源管理器
 * 用于创建和管理Topic、Consumer Group等资源
 */
@Slf4j
public class MQResourceManager {

    private final DefaultMQAdminExt admin;
    private final String nameServer;

    public MQResourceManager(String nameServer) {
        this.nameServer = nameServer;
        this.admin = new DefaultMQAdminExt();
        this.admin.setInstanceName("MQResourceManager_" + System.currentTimeMillis());
        this.admin.setNamesrvAddr(nameServer);
    }

    /**
     * 启动管理工具
     */
    public void start() throws Exception {
        admin.start();
        log.info("MQ资源管理器启动成功，NameServer: {}", nameServer);
    }

    /**
     * 关闭管理工具
     */
    public void shutdown() {
        if (admin != null) {
            admin.shutdown();
            log.info("MQ资源管理器已关闭");
        }
    }

    /**
     * 检查Topic是否存在
     */
    public boolean topicExists(String topic) {
        try {
            TopicRouteData routeData = admin.examineTopicRouteInfo(topic);
            return routeData != null && !routeData.getBrokerDatas().isEmpty();
        } catch (Exception e) {
            log.debug("检查Topic存在性失败: {}, 原因: {}", topic, e.getMessage());
            return false;
        }
    }

    /**
     * 创建Topic
     */
    public boolean createTopic(String topic, int queueNums, int perm) {
        try {
            // 获取集群信息
            var clusterInfo = admin.examineBrokerClusterInfo();
            if (clusterInfo.getBrokerAddrTable().isEmpty()) {
                log.error("没有找到可用的Broker");
                return false;
            }

            // 为每个Broker创建Topic
            boolean success = true;
            for (BrokerData brokerData : clusterInfo.getBrokerAddrTable().values()) {
                String masterAddr = brokerData.getBrokerAddrs().get(0L); // Master Broker
                if (masterAddr != null) {
                    try {
                        TopicConfig topicConfig = new TopicConfig();
                        topicConfig.setTopicName(topic);
                        topicConfig.setReadQueueNums(queueNums);
                        topicConfig.setWriteQueueNums(queueNums);
                        topicConfig.setPerm(perm);

                        admin.createAndUpdateTopicConfig(masterAddr, topicConfig);
                        log.info("在Broker {}上创建Topic {} 成功", brokerData.getBrokerName(), topic);
                    } catch (Exception e) {
                        log.error("在Broker {}上创建Topic {} 失败", brokerData.getBrokerName(), topic, e);
                        success = false;
                    }
                }
            }

            if (success) {
                log.info("Topic {} 创建成功，队列数: {}", topic, queueNums);
                // 等待Topic路由信息更新
                Thread.sleep(2000);
            }

            return success;
        } catch (Exception e) {
            log.error("创建Topic {} 失败", topic, e);
            return false;
        }
    }

    /**
     * 创建Topic（使用默认参数）
     */
    public boolean createTopic(String topic) {
        // 默认参数：8个队列，读写权限
        return createTopic(topic, 8, 6); // 6 = READ_WRITE权限
    }

    /**
     * 创建或确保Topic存在
     */
    public boolean ensureTopicExists(String topic) {
        if (topicExists(topic)) {
            log.info("Topic {} 已存在", topic);
            return true;
        }

        log.info("Topic {} 不存在，开始创建...", topic);
        return createTopic(topic);
    }

    /**
     * 打印Topic信息
     */
    public void printTopicInfo(String topic) {
        try {
            if (!topicExists(topic)) {
                log.info("Topic {} 不存在", topic);
                return;
            }

            TopicRouteData routeData = admin.examineTopicRouteInfo(topic);
            log.info("=== Topic {} 信息 ===", topic);
            log.info("Broker数量: {}", routeData.getBrokerDatas().size());
            log.info("队列数量: {}", routeData.getQueueDatas().size());

            routeData.getBrokerDatas().forEach(brokerData -> {
                log.info("Broker: {}, 地址: {}", brokerData.getBrokerName(), brokerData.getBrokerAddrs());
            });

            routeData.getQueueDatas().forEach(queueData -> {
                log.info("队列: Broker={}, ReadQueueNums={}, WriteQueueNums={}",
                        queueData.getBrokerName(), queueData.getReadQueueNums(), queueData.getWriteQueueNums());
            });

        } catch (Exception e) {
            log.error("获取Topic {} 信息失败", topic, e);
        }
    }

    /**
     * 检查Consumer Group是否存在
     */
    public boolean consumerGroupExists(String consumerGroup) {
        try {
            // 尝试获取消费者连接信息
            var clusterInfo = admin.examineBrokerClusterInfo();
            for (BrokerData brokerData : clusterInfo.getBrokerAddrTable().values()) {
                String addr = brokerData.selectBrokerAddr();
                if (addr != null) {
                    try {
                        var connection = admin.examineConsumerConnectionInfo(consumerGroup);
                        if (connection != null && !connection.getConnectionSet().isEmpty()) {
                            return true;
                        }
                    } catch (Exception e) {
                        // 继续检查下一个Broker
                        log.debug("在Broker {}上检查消费组 {} 失败: {}", addr, consumerGroup, e.getMessage());
                    }
                }
            }
            return false;
        } catch (Exception e) {
            log.debug("检查消费组存在性失败: {}, 原因: {}", consumerGroup, e.getMessage());
            return false;
        }
    }

    /**
     * 创建消费组订阅关系
     */
    public boolean createConsumerGroup(String consumerGroup, boolean isBroadcast) {
        try {
            var clusterInfo = admin.examineBrokerClusterInfo();
            boolean success = true;

            for (BrokerData brokerData : clusterInfo.getBrokerAddrTable().values()) {
                String masterAddr = brokerData.getBrokerAddrs().get(0L); // Master Broker
                if (masterAddr != null) {
                    try {
                        // 创建订阅组配置
                        com.alibaba.rocketmq.common.subscription.SubscriptionGroupConfig config =
                            new com.alibaba.rocketmq.common.subscription.SubscriptionGroupConfig();
                        config.setGroupName(consumerGroup);
                        config.setConsumeEnable(true);
                        config.setConsumeBroadcastEnable(isBroadcast);
                        config.setConsumeFromMinEnable(true);

                        // 在Broker上创建订阅组
                        admin.createAndUpdateSubscriptionGroupConfig(masterAddr, config);
                        log.info("在Broker {}上创建消费组 {} 成功", brokerData.getBrokerName(), consumerGroup);
                    } catch (Exception e) {
                        log.error("在Broker {}上创建消费组 {} 失败", brokerData.getBrokerName(), consumerGroup, e);
                        success = false;
                    }
                }
            }

            if (success) {
                log.info("消费组 {} 创建成功，广播模式: {}", consumerGroup, isBroadcast);
                // 等待配置同步
                Thread.sleep(2000);
            }

            return success;
        } catch (Exception e) {
            log.error("创建消费组 {} 失败", consumerGroup, e);
            return false;
        }
    }

    /**
     * 确保消费组存在
     */
    public boolean ensureConsumerGroupExists(String consumerGroup, boolean isBroadcast) {
        // 检查消费组是否已存在
        if (consumerGroupExists(consumerGroup)) {
            log.info("消费组 {} 已存在", consumerGroup);
            return true;
        }

        log.info("消费组 {} 不存在，开始创建...", consumerGroup);
        return createConsumerGroup(consumerGroup, isBroadcast);
    }

    /**
     * 确保消费组存在（默认广播模式）
     */
    public boolean ensureConsumerGroupExists(String consumerGroup) {
        return ensureConsumerGroupExists(consumerGroup, true);
    }
}
