package cn.voriya;

import com.ly.tcbase.cacheclient.CacheClientHA;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

public class CacheClientHATest {
    private final CacheClientHA client = new CacheClientHA("tp_hd", false);
    @SneakyThrows
    @Test
    public void test() {
        client.pubsub().publish("test1", "test");
        client.pubsub().publish("test2", "test");
        client.pubsub().addListener((channel, message) -> System.out.println(STR."channel: \{channel}, message: \{message}"));
        client.pubsub().subscribe("test1","test2");
    }
}
