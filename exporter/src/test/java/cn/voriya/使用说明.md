# RocketMQ广播模式测试使用说明

## 配置信息
- **NameServer**: `mqnameserver.uat.17usoft.com:9876`
- **Topic**: `test_topic_broadcast_1000msg`
- **Producer Group**: `test_producer_group_1000msg`
- **Consumer Group**: `test_broadcast_group_1000msg`
- **消息发送频率**: 1000条/分钟 (约每60毫秒发送1条)
- **消费模式**: 广播模式 (BROADCASTING)

## 解决的问题
之前遇到的错误：
```
com.alibaba.rocketmq.client.exception.MQBrokerException: CODE: 26  DESC: subscription group not exist, test_broadcast_group_1000msg
```

现在通过以下方式解决：
1. 在启动消费者前，自动创建消费组
2. 使用`MQResourceManager`管理Topic和消费组的创建
3. 确保广播模式的消费组配置正确

## 使用步骤

### 方式一：使用JUnit测试（推荐）

1. **初始化资源**
   ```bash
   # 创建Topic和消费组
   mvn test -Dtest=BroadcastMQTest#initMQResources
   ```

2. **简单测试验证**
   ```bash
   # 发送10条测试消息
   mvn test -Dtest=BroadcastMQTest#simpleProducerTest
   
   # 启动简单消费者测试（在另一个终端）
   mvn test -Dtest=BroadcastMQTest#simpleBroadcastConsumerTest
   ```

3. **完整性能测试**
   ```bash
   # 启动生产者（每分钟1000条消息，运行10分钟）
   mvn test -Dtest=BroadcastMQTest#startProducer
   
   # 启动消费者（广播模式，运行10分钟）
   mvn test -Dtest=BroadcastMQTest#startBroadcastConsumer
   ```

### 方式二：直接运行独立程序

1. **启动生产者**
   ```bash
   # 编译并运行生产者（会自动创建Topic和消费组）
   mvn compile exec:java -Dexec.mainClass="cn.voriya.BroadcastProducer"
   ```

2. **启动消费者**
   ```bash
   # 编译并运行消费者（在另一个终端）
   mvn compile exec:java -Dexec.mainClass="cn.voriya.BroadcastConsumer"
   ```

## 文件说明

### 1. MQResourceManager.java
MQ资源管理器，核心功能：
- `ensureTopicExists(topic)`: 检查并创建Topic
- `ensureConsumerGroupExists(group, isBroadcast)`: 检查并创建消费组
- `createConsumerGroup(group, isBroadcast)`: 创建消费组配置
- `printTopicInfo(topic)`: 打印Topic详细信息

### 2. BroadcastMQTest.java
JUnit测试类，包含：
- `initMQResources()`: 初始化所有MQ资源
- `simpleProducerTest()`: 简单生产者测试
- `simpleBroadcastConsumerTest()`: 简单消费者测试
- `startProducer()`: 完整生产者测试
- `startBroadcastConsumer()`: 完整消费者测试

### 3. BroadcastProducer.java
独立生产者程序：
- 自动创建Topic和消费组
- 每分钟发送1000条消息
- 实时统计发送情况
- 支持优雅关闭

### 4. BroadcastConsumer.java
独立消费者程序：
- 检查并创建消费组
- 广播模式消费
- 实时统计消费情况
- 支持优雅关闭

## 验证广播模式

1. **启动多个消费者实例**：
   ```bash
   # 终端1
   mvn compile exec:java -Dexec.mainClass="cn.voriya.BroadcastConsumer"
   
   # 终端2
   mvn compile exec:java -Dexec.mainClass="cn.voriya.BroadcastConsumer"
   
   # 终端3
   mvn compile exec:java -Dexec.mainClass="cn.voriya.BroadcastConsumer"
   ```

2. **启动生产者**：
   ```bash
   # 终端4
   mvn compile exec:java -Dexec.mainClass="cn.voriya.BroadcastProducer"
   ```

3. **验证结果**：
   - 每个消费者实例都应该收到所有消息
   - 消息不会在消费者之间负载均衡
   - 每条消息会被所有消费者实例消费

## 监控信息

### 生产者监控
- 每100条消息打印发送日志
- 每分钟统计发送总数和成功率
- 显示实时发送速率

### 消费者监控
- 每100条消息打印消费日志
- 每分钟统计消费总数
- 显示消息延迟信息

## 注意事项

1. **首次运行**：建议先运行`initMQResources()`或直接运行生产者来创建必要的资源

2. **网络连接**：确保能够访问NameServer地址

3. **权限要求**：需要有创建Topic和消费组的权限

4. **资源清理**：测试完成后可以手动删除测试用的Topic和消费组

5. **并发测试**：可以同时启动多个消费者实例验证广播模式

## 故障排除

### 常见问题及解决方案

1. **消费组不存在错误**：
   - 运行`initMQResources()`创建资源
   - 或者直接运行生产者（会自动创建）

2. **Topic不存在错误**：
   - 检查NameServer连接
   - 确认有创建Topic的权限

3. **连接超时**：
   - 检查网络连接
   - 确认NameServer地址正确

4. **消息收不到**：
   - 确认消费者是广播模式
   - 检查Topic和消费组是否正确创建
   - 查看消费者日志确认启动状态

## 性能调优

- 调整`MESSAGES_PER_MINUTE`常量修改发送频率
- 修改队列数量优化并发性能
- 调整消费者线程数提高消费能力
- 根据需要修改消息体大小和内容
