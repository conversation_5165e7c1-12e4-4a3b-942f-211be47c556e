package cn.voriya;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.junit.jupiter.api.Test;

import java.util.concurrent.TimeUnit;

public class CaffeineTest {
    
    @Test
    public void stringCacheTest() {
        Cache<String, String> cache = Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.SECONDS)
                .maximumSize(100)
                 .build(key -> "loaded_" + key);
        
        // 测试基本的put和get操作
        cache.put("key1", "value1");
        cache.put("key2", "value2");
        
        System.out.println("key1: " + cache.getIfPresent("key1"));
        System.out.println("key2: " + cache.getIfPresent("key2"));
        System.out.println("key3: " + cache.getIfPresent("key3")); // null
        
        // 测试get with loader
        String value = cache.get("key3", k -> "default_" + k);
        System.out.println("key3 with loader: " + value);
        System.out.println("key3: " + cache.getIfPresent("key3"));
        
        // 测试缓存统计
        System.out.println("Cache size: " + cache.estimatedSize());
    }
    @Test
    public void loadingCacheTest() {
        LoadingCache<String, String> cache = Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.SECONDS)
                .maximumSize(100)
                .build(key -> "loaded_" + key);

        // 测试默认加载器
        String loadedValue = cache.get("key1");
        System.out.println("key1: " + loadedValue);

        // 手动put后再get
        cache.put("key2", "manual_value");
        System.out.println("key2: " + cache.get("key2"));
    }
}