package cn.voriya;

import cn.voriya.boot.VoriyaBoot;
import cn.voriya.boot.cache.RedisCache;
import cn.voriya.exporter.cache.impl.TCBaseClient;
import org.junit.jupiter.api.Test;

import java.util.List;

public class RedisTest {
    @Test
    public void test() {
        VoriyaBoot.initialize(TCBaseClient.getInstance(),"cn.voriya");
        List<String> list = RedisCache.getClient().scan("*");
        list.forEach(System.out::println);
        System.out.println(RedisCache.getClient().ttl("2:q:ttl:lock:{importer-task-list}:LY-MQ-PUBLIC-1"));
        System.out.println(RedisCache.getClient().ttl("sfsfsfsfsewr3w2ew3gxfsfs"));
        System.out.println(RedisCache.getClient().ttl("2:q:tsk:bcgrp:{LY-MQ-PUBLIC-UAT}"));

    }
}
