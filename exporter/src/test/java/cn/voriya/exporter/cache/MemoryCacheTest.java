package cn.voriya.exporter.cache;

import cn.voriya.exporter.Application;
import cn.voriya.exporter.client.TurboClient;
import com.alibaba.rocketmq.common.protocol.route.BrokerData;
import com.github.benmanes.caffeine.cache.Cache;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.lang.reflect.Field;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MemoryCache 功能测试类
 * 测试缓存的基本功能、过期机制、并发安全性等
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class MemoryCacheTest {

    private static final String TEST_TOPIC = "test-topic-memory-cache";
    
    @BeforeAll
    static void setUp() {
        Application.initialize();
    }

    @AfterAll
    static void tearDown() {
        if (TurboClient.getInstance() != null) {
            TurboClient.getInstance().close();
        }
    }

    @Test
    @Order(1)
    @DisplayName("基本缓存功能测试")
    void testBasicCacheFunction() {
        // 测试缓存的基本get操作
        List<BrokerData> brokerList = MemoryCache.getTopicRouteBrokerList(TEST_TOPIC);
        assertNotNull(brokerList, "缓存返回结果不应为null");
        
        // 再次获取，应该从缓存中获取
        List<BrokerData> cachedBrokerList = MemoryCache.getTopicRouteBrokerList(TEST_TOPIC);
        assertNotNull(cachedBrokerList, "缓存返回结果不应为null");
        
        // 验证两次获取的结果是否一致（引用相同表示从缓存获取）
        assertSame(brokerList, cachedBrokerList, "第二次获取应该从缓存中返回相同的对象引用");
    }

    @Test
    @Order(2)
    @DisplayName("缓存过期测试")
    void testCacheExpiration() throws InterruptedException {
        String expireTestTopic = "expire-test-topic";
        
        // 首次获取数据
        List<BrokerData> firstResult = MemoryCache.getTopicRouteBrokerList(expireTestTopic);
        assertNotNull(firstResult);
        
        // 等待一小段时间，确保缓存还未过期
        Thread.sleep(1000);
        List<BrokerData> secondResult = MemoryCache.getTopicRouteBrokerList(expireTestTopic);
        assertSame(firstResult, secondResult, "缓存未过期时应返回相同对象");
        
        // 注意：由于缓存过期时间是300秒，在单元测试中无法等待这么长时间
        // 这里主要测试缓存机制是否正常工作
        System.out.println("缓存过期测试：缓存TTL为300秒，在单元测试中验证缓存机制正常工作");
    }

    @Test
    @Order(3)
    @DisplayName("并发访问安全性测试")
    @Execution(ExecutionMode.CONCURRENT)
    void testConcurrentAccess() throws InterruptedException {
        String concurrentTestTopic = "concurrent-test-topic";
        int threadCount = 10;
        int requestsPerThread = 100;

        try (ExecutorService executor = Executors.newFixedThreadPool(threadCount)) {
            CountDownLatch latch = new CountDownLatch(threadCount);
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger errorCount = new AtomicInteger(0);

            for (int i = 0; i < threadCount; i++) {
                executor.submit(() -> {
                    try {
                        for (int j = 0; j < requestsPerThread; j++) {
                            List<BrokerData> result = MemoryCache.getTopicRouteBrokerList(concurrentTestTopic);
                            assertNotNull(result);
                            successCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                        e.printStackTrace();
                    } finally {
                        latch.countDown();
                    }
                });
            }

            assertTrue(latch.await(30, TimeUnit.SECONDS), "并发测试应在30秒内完成");

            int expectedSuccessCount = threadCount * requestsPerThread;
            assertEquals(expectedSuccessCount, successCount.get(),
                    "所有请求都应该成功");
            assertEquals(0, errorCount.get(), "不应该有错误发生");

            executor.shutdown();
            assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));
        }
    }

    @Test
    @Order(4)
    @DisplayName("异常处理测试")
    void testExceptionHandling() {
        // 测试无效topic的处理
        String invalidTopic = "invalid-topic-that-does-not-exist-12345";
        
        assertDoesNotThrow(() -> {
            List<BrokerData> result = MemoryCache.getTopicRouteBrokerList(invalidTopic);
            assertNotNull(result, "即使topic不存在，也应返回非null结果");
            // 根据实现，应该返回空列表
            assertTrue(result.isEmpty() || !result.isEmpty(), "结果应该是有效的列表");
        }, "处理无效topic时不应抛出异常");
    }

    @Test
    @Order(5)
    @DisplayName("缓存内部状态测试")
    void testCacheInternalState() throws Exception {
        // 使用反射访问缓存内部状态
        Field cacheField = MemoryCache.class.getDeclaredField("topicRouteBrokerListCache");
        cacheField.setAccessible(true);
        
        Cache<String, List<BrokerData>> cache = (Cache<String, List<BrokerData>>) cacheField.get(null);
        
        String testTopic = "internal-state-test-topic";
        
        // 初始状态：缓存应该为空或不包含测试topic
        long initialSize = cache.estimatedSize();
        
        // 触发缓存加载
        MemoryCache.getTopicRouteBrokerList(testTopic);
        
        // 验证缓存大小增加
        long afterSize = cache.estimatedSize();
        assertTrue(afterSize >= initialSize, "缓存大小应该增加或保持不变");
        
        // 验证缓存中包含我们的key
        assertNotNull(cache.getIfPresent(testTopic), "缓存中应该包含测试topic");
    }

    @Test
    @Order(6)
    @DisplayName("多个不同Topic缓存测试")
    void testMultipleTopicsCache() {
        String[] testTopics = {
                "topic-1-multi-test",
                "topic-2-multi-test", 
                "topic-3-multi-test"
        };
        
        // 为每个topic获取缓存数据
        for (String topic : testTopics) {
            List<BrokerData> result = MemoryCache.getTopicRouteBrokerList(topic);
            assertNotNull(result, "每个topic都应该返回非null结果");
        }
        
        // 验证每个topic的缓存都是独立的
        for (int i = 0; i < testTopics.length; i++) {
            for (int j = i + 1; j < testTopics.length; j++) {
                List<BrokerData> result1 = MemoryCache.getTopicRouteBrokerList(testTopics[i]);
                List<BrokerData> result2 = MemoryCache.getTopicRouteBrokerList(testTopics[j]);
                
                // 注意：这里不能简单比较引用，因为不同topic可能返回相同的BrokerData列表
                // 主要验证缓存机制正常工作
                assertNotNull(result1);
                assertNotNull(result2);
            }
        }
    }

    @Test
    @Order(7)
    @DisplayName("缓存性能基准测试")
    void testCachePerformance() {
        String perfTestTopic = "performance-test-topic";
        int warmupRounds = 100;
        int testRounds = 1000;
        
        // 预热
        for (int i = 0; i < warmupRounds; i++) {
            MemoryCache.getTopicRouteBrokerList(perfTestTopic);
        }
        
        // 性能测试
        long startTime = System.nanoTime();
        for (int i = 0; i < testRounds; i++) {
            MemoryCache.getTopicRouteBrokerList(perfTestTopic);
        }
        long endTime = System.nanoTime();
        
        long totalTimeMs = (endTime - startTime) / 1_000_000;
        double avgTimeMs = (double) totalTimeMs / testRounds;
        
        System.out.printf("缓存性能测试结果：%d次调用总耗时%dms，平均每次%.3fms%n", 
                testRounds, totalTimeMs, avgTimeMs);
        
        // 缓存命中的平均响应时间应该很快（小于1ms）
        assertTrue(avgTimeMs < 1.0, 
                String.format("缓存命中平均响应时间应小于1ms，实际：%.3fms", avgTimeMs));
    }
}
