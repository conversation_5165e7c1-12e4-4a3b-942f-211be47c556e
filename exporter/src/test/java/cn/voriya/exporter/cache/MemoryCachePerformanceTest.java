package cn.voriya.exporter.cache;

import cn.voriya.exporter.Application;
import cn.voriya.exporter.client.TurboClient;
import com.alibaba.rocketmq.common.protocol.route.BrokerData;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.junit.jupiter.api.*;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MemoryCache 性能和资源占用测试类
 * 专门测试缓存的性能表现和内存使用情况
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class MemoryCachePerformanceTest {

    private MemoryMXBean memoryBean;

    @BeforeAll
    static void setUp() {
        Application.initialize();
    }

    @BeforeEach
    void setUpEach() {
        memoryBean = ManagementFactory.getMemoryMXBean();
        // 强制垃圾回收，获得更准确的内存测量
        System.gc();
        MemoryCache.clear();
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @AfterAll
    static void tearDown() {
        if (TurboClient.getInstance() != null) {
            TurboClient.getInstance().close();
        }
    }

    /**
     * 创建mock的BrokerData列表，用于测试
     */
    private List<BrokerData> createMockBrokerDataList() {
        List<BrokerData> brokerList = new ArrayList<>();

        // 创建10个mock的broker
        for (int i = 1; i <= 10; i++) {
            BrokerData brokerData = new BrokerData();
            brokerData.setBrokerName("mock-broker-" + i);

            // 创建broker地址映射
            HashMap<Long, String> brokerAddrs = new HashMap<>();
            HashMap<Long,String> brokerIdLogicIdc = new HashMap<>();
            brokerAddrs.put(0L, "192.168.1." + (10 + i) + ":10911"); // master
            brokerAddrs.put(1L, "192.168.1." + (20 + i) + ":10911"); // slave
            brokerIdLogicIdc.put(0L, "officeidc_hd1");
            brokerIdLogicIdc.put(1L, "officeidc_hd1");
            brokerData.setBrokerIdLogicIdc(brokerIdLogicIdc);
            brokerData.setBrokerAddrs(brokerAddrs);

            brokerList.add(brokerData);
        }

        return brokerList;
    }

    @Test
    @Order(1)
    @DisplayName("高并发性能压力测试")
    void testHighConcurrencyPerformance() throws InterruptedException {
        String topic = "high-concurrency-perf-test";
        int threadCount = 50;
        int requestsPerThread = 200;
        int totalRequests = threadCount * requestsPerThread;

        try (ExecutorService executor = Executors.newFixedThreadPool(threadCount)) {
            CountDownLatch startLatch = new CountDownLatch(1);
            CountDownLatch endLatch = new CountDownLatch(threadCount);

            AtomicLong totalResponseTime = new AtomicLong(0);
            AtomicLong successCount = new AtomicLong(0);
            AtomicLong errorCount = new AtomicLong(0);

            // 预热缓存
            MemoryCache.getTopicRouteBrokerList(topic);

            long testStartTime = System.nanoTime();

            for (int i = 0; i < threadCount; i++) {
                executor.submit(() -> {
                    try {
                        startLatch.await(); // 等待统一开始信号

                        for (int j = 0; j < requestsPerThread; j++) {
                            long requestStart = System.nanoTime();
                            try {
                                List<BrokerData> result = MemoryCache.getTopicRouteBrokerList(topic);
                                assertNotNull(result);
                                successCount.incrementAndGet();
                            } catch (Exception e) {
                                errorCount.incrementAndGet();
                            }
                            long requestEnd = System.nanoTime();
                            totalResponseTime.addAndGet(requestEnd - requestStart);
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        errorCount.incrementAndGet();
                    } finally {
                        endLatch.countDown();
                    }
                });
            }

            // 开始测试
            startLatch.countDown();
            assertTrue(endLatch.await(60, TimeUnit.SECONDS), "高并发测试应在60秒内完成");

            long testEndTime = System.nanoTime();
            long totalTestTime = testEndTime - testStartTime;

            // 计算性能指标
            double totalTestTimeMs = totalTestTime / 1_000_000.0;
            double avgResponseTimeMs = (totalResponseTime.get() / 1_000_000.0) / successCount.get();
            double throughput = (successCount.get() * 1000.0) / totalTestTimeMs;

            System.out.printf("高并发性能测试结果：%n");
            System.out.printf("  总请求数: %d%n", totalRequests);
            System.out.printf("  成功请求数: %d%n", successCount.get());
            System.out.printf("  失败请求数: %d%n", errorCount.get());
            System.out.printf("  总测试时间: %.2fms%n", totalTestTimeMs);
            System.out.printf("  平均响应时间: %.3fms%n", avgResponseTimeMs);
            System.out.printf("  吞吐量: %.2f requests/sec%n", throughput);

            // 性能断言
            assertEquals(totalRequests, successCount.get(), "所有请求都应该成功");
            assertEquals(0, errorCount.get(), "不应该有失败请求");
            assertTrue(avgResponseTimeMs < 5.0,
                    String.format("平均响应时间应小于5ms，实际：%.3fms", avgResponseTimeMs));
            assertTrue(throughput > 1000,
                    String.format("吞吐量应大于1000 req/sec，实际：%.2f", throughput));

            executor.shutdown();
            assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS));
        }
    }

    @Test
    @Order(2)
    @DisplayName("内存使用量测试")
    void testMemoryUsage() throws Exception {
        // 获取初始内存使用情况
        MemoryUsage beforeHeap = memoryBean.getHeapMemoryUsage();
        long beforeUsed = beforeHeap.getUsed();

        // 获取缓存实例
        Field cacheField = MemoryCache.class.getDeclaredField("topicRouteBrokerListCache");
        cacheField.setAccessible(true);
        DynamicCache<String, List<BrokerData>> dynamicCache = (DynamicCache<String, List<BrokerData>>) cacheField.get(null);
        LoadingCache<String, List<BrokerData>> cache = dynamicCache.getCacheInstance();

        long initialCacheSize = cache.estimatedSize();

        // 创建mock的BrokerData列表
        List<BrokerData> mockBrokerList = createMockBrokerDataList();

        // 创建大量不同的topic来填充缓存
        int topicCount = 10000;
        List<String> topics = IntStream.range(0, topicCount)
                .mapToObj(i -> "memory-test-topic-" + i)
                .toList();

        // 直接向缓存中放入mock数据，避免真实的网络调用
        for (String topic : topics) {
            cache.put(topic, createMockBrokerDataList());
        }

        // 获取填充后的内存使用情况
        System.gc();
        Thread.sleep(200);
        MemoryUsage afterHeap = memoryBean.getHeapMemoryUsage();
        long afterUsed = afterHeap.getUsed();

        long memoryIncrease = afterUsed - beforeUsed;
        double memoryIncreaseKB = memoryIncrease / 1024.0;
        double avgMemoryPerTopic = memoryIncreaseKB / topicCount;

        long estimateMemoryUsageWithJOL = dynamicCache.estimateMemoryUsageWithJOL();
        double estimateMemoryUsageWithJOLKB = estimateMemoryUsageWithJOL / 1024.0;
        double estimateMemoryWithJOLPerTopic = estimateMemoryUsageWithJOLKB / topicCount;

        long estimateMemoryUsageImproved = dynamicCache.estimateMemoryUsageImproved();
        double estimateMemoryUsageImprovedKB = estimateMemoryUsageImproved / 1024.0;
        double estimateMemoryImprovedPerTopic = estimateMemoryUsageImprovedKB / topicCount;

        long finalCacheSize = cache.estimatedSize();

        System.out.printf("内存使用测试结果：%n");
        System.out.printf("  缓存topic数量: %d%n", topicCount);
        System.out.printf("  初始缓存大小: %d%n", initialCacheSize);
        System.out.printf("  最终缓存大小: %d%n", finalCacheSize);
        //
        System.out.printf("  内存增长: %.2f KB%n", memoryIncreaseKB);
        System.out.printf("  平均每个topic内存占用: %.3f KB%n", avgMemoryPerTopic);
        //
        System.out.printf("  内存占用(JOL): %.2f KB%n", estimateMemoryUsageWithJOLKB);
        System.out.printf("  平均每个topic内存占用(JOL): %.3f KB%n", estimateMemoryWithJOLPerTopic);
        //
        System.out.printf("  内存占用(改进): %.2f KB%n", estimateMemoryUsageImprovedKB);
        System.out.printf("  平均每个topic内存占用(改进): %.3f KB%n", estimateMemoryImprovedPerTopic);


        // 验证缓存确实被填充了
        assertTrue(finalCacheSize >= initialCacheSize + topicCount,
                "缓存大小应该增加至少" + topicCount + "个条目");

        // 验证缓存中的数据
        for (int i = 0; i < 10; i++) {
            String testTopic = "memory-test-topic-" + i;
            List<BrokerData> cachedData = cache.getIfPresent(testTopic);
            assertNotNull(cachedData, "缓存中应该包含topic: " + testTopic);
            assertEquals(mockBrokerList.size(), cachedData.size(), "缓存的数据大小应该匹配");
        }

        // 内存使用应该在合理范围内
        assertTrue(memoryIncreaseKB < 100 * 1024, // 50MB
                String.format("内存增长应小于100MB，实际：%.2fKB", memoryIncreaseKB));
        assertTrue(avgMemoryPerTopic < 50, // 50KB per topic
                String.format("平均每个topic内存占用应小于50KB，实际：%.3fKB", avgMemoryPerTopic));
    }

    @Test
    @Order(3)
    @DisplayName("缓存命中率测试")
    void testCacheHitRate() throws Exception {
        String[] testTopics = {"hit-rate-topic-1", "hit-rate-topic-2", "hit-rate-topic-3"};
        int requestsPerTopic = 100;

        // 预热 - 确保所有topic都被缓存
        for (String topic : testTopics) {
            MemoryCache.getTopicRouteBrokerList(topic);
        }

        // 获取缓存统计信息（如果Caffeine配置了统计）
        Field cacheField = MemoryCache.class.getDeclaredField("topicRouteBrokerListCache");
        cacheField.setAccessible(true);
        DynamicCache<String, List<BrokerData>> dynamicCache = (DynamicCache<String, List<BrokerData>>) cacheField.get(null);
        LoadingCache<String, List<BrokerData>> cache = dynamicCache.getCacheInstance();

        long initialSize = cache.estimatedSize();

        // 执行大量重复请求
        long startTime = System.nanoTime();
        for (int i = 0; i < requestsPerTopic; i++) {
            for (String topic : testTopics) {
                List<BrokerData> result = MemoryCache.getTopicRouteBrokerList(topic);
                assertNotNull(result);
            }
        }
        long endTime = System.nanoTime();

        long finalSize = cache.estimatedSize();
        double totalTimeMs = (endTime - startTime) / 1_000_000.0;
        int totalRequests = testTopics.length * requestsPerTopic;
        double avgTimeMs = totalTimeMs / totalRequests;

        System.out.printf("缓存命中率测试结果：%n");
        System.out.printf("  测试topic数: %d%n", testTopics.length);
        System.out.printf("  每个topic请求数: %d%n", requestsPerTopic);
        System.out.printf("  总请求数: %d%n", totalRequests);
        System.out.printf("  初始缓存大小: %d%n", initialSize);
        System.out.printf("  最终缓存大小: %d%n", finalSize);
        System.out.printf("  总耗时: %.2fms%n", totalTimeMs);
        System.out.printf("  平均响应时间: %.3fms%n", avgTimeMs);

        // 缓存大小不应该增长（表示命中率高）
        assertEquals(initialSize, finalSize, "缓存大小不应该增长，表示所有请求都命中缓存");

        // 平均响应时间应该很快（缓存命中）
        assertTrue(avgTimeMs < 0.1,
                String.format("缓存命中的平均响应时间应小于0.1ms，实际：%.3fms", avgTimeMs));
    }


    @Test
    @Order(4)
    @DisplayName("垃圾回收影响测试")
    void testGarbageCollectionImpact() throws InterruptedException {
        String topic = "gc-impact-test";
        int iterations = 1000;

        // 记录GC前的内存状态
        MemoryUsage beforeGC = memoryBean.getHeapMemoryUsage();

        // 执行大量缓存操作
        List<Long> responseTimes = new ArrayList<>();
        for (int i = 0; i < iterations; i++) {
            long start = System.nanoTime();
            MemoryCache.getTopicRouteBrokerList(topic + "-" + (i % 10)); // 使用10个不同的topic循环
            long end = System.nanoTime();
            responseTimes.add((end - start) / 1_000_000);

            // 每100次操作触发一次GC
            if (i % 100 == 0) {
                System.gc();
                Thread.sleep(10);
            }
        }

        // 记录GC后的内存状态
        System.gc();
        Thread.sleep(100);
        MemoryUsage afterGC = memoryBean.getHeapMemoryUsage();

        // 分析响应时间分布
        double avgResponseTime = responseTimes.stream()
                .mapToLong(Long::longValue)
                .average()
                .orElse(0.0);

        long maxResponseTime = responseTimes.stream()
                .mapToLong(Long::longValue)
                .max()
                .orElse(0L);

        // 计算95百分位响应时间
        responseTimes.sort(Long::compareTo);
        long p95ResponseTime = responseTimes.get((int) (responseTimes.size() * 0.95));

        System.out.printf("垃圾回收影响测试结果：%n");
        System.out.printf("  测试迭代数: %d%n", iterations);
        System.out.printf("  GC前内存使用: %.2f MB%n", beforeGC.getUsed() / 1024.0 / 1024.0);
        System.out.printf("  GC后内存使用: %.2f MB%n", afterGC.getUsed() / 1024.0 / 1024.0);
        System.out.printf("  平均响应时间: %.3fms%n", avgResponseTime);
        System.out.printf("  最大响应时间: %dms%n", maxResponseTime);
        System.out.printf("  95百分位响应时间: %dms%n", p95ResponseTime);

        // GC影响断言
        assertTrue(avgResponseTime < 5.0,
                String.format("即使有GC，平均响应时间也应小于5ms，实际：%.3fms", avgResponseTime));
        assertTrue(p95ResponseTime < 20,
                String.format("95百分位响应时间应小于20ms，实际：%dms", p95ResponseTime));
    }
}
