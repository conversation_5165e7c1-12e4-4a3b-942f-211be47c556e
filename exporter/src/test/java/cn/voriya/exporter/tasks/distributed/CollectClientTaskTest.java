package cn.voriya.exporter.tasks.distributed;

import cn.voriya.boot.VoriyaBoot;
import cn.voriya.exporter.Application;
import cn.voriya.exporter.cache.impl.TCBaseClient;
import cn.voriya.exporter.client.TurboClient;
import org.junit.jupiter.api.Test;

public class CollectClientTaskTest {
    @Test
    public void test() {
        TurboClient turboClient = TurboClient.getInstance();
        String clusterName = turboClient.getClusterName();
        VoriyaBoot.initialize(TCBaseClient.getInstance(), Application.class.getPackageName());
        VoriyaBoot.registerClusterName(clusterName);
        new CollectClientTask().command();
    }
}
