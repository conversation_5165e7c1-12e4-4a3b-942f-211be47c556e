package cn.voriya;

import cn.voriya.boot.VoriyaBoot;
import cn.voriya.exporter.cache.impl.TCBaseClient;
import cn.voriya.exporter.client.TurboClient;
import org.junit.jupiter.api.Test;

import java.util.Set;

public class MQTest {
    @Test
    public void getTest(){
        VoriyaBoot.initialize(TCBaseClient.getInstance(), "cn.voriya");
        try (TurboClient client = new TurboClient("mqnameserverbak.17usoft.com:9876")) {
            Set<String> brokersIdc = client.getClusterBrokerAzs();
            System.out.println(brokersIdc);
        }
    }
}
