package cn.voriya;

import cn.voriya.boot.cache.RedisCache;
import cn.voriya.cache.lua.LuaListTest;
import cn.voriya.cache.lua.LuaTest;
import org.junit.jupiter.api.Test;

public class JedisTest {
    @Test
    public void scriptTest() {
        System.out.println(RedisCache.getClient().evalsha(new LuaTest("test", "1111")));
        System.out.println(RedisCache.getClient().evalsha(new LuaTest("test", "3333")));
        Object evalsha = RedisCache.getClient().evalsha(new LuaListTest("list-test", "2222"));
        System.out.println(evalsha);
    }
}
