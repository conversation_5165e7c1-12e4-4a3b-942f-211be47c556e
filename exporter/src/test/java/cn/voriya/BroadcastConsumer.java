package cn.voriya;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.common.consumer.ConsumeFromWhere;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 广播模式消费者
 * 使用广播模式消费消息，每个消费者实例都会收到所有消息
 */
@Slf4j
public class BroadcastConsumer {

    private static final String NAME_SERVER = "mqnameserver.uat.17usoft.com:9876";
    private static final String TOPIC = "test_topic_broadcast_1000msg";
    private static final String CONSUMER_GROUP = "test_broadcast_group_1000msg";

    public static void main(String[] args) throws Exception {
        // 首先检查Topic和消费组是否存在
        MQResourceManager resourceManager = new MQResourceManager(NAME_SERVER);
        try {
            resourceManager.start();
            log.info("检查Topic和消费组是否存在...");

            // 检查Topic
            if (!resourceManager.topicExists(TOPIC)) {
                log.error("Topic {} 不存在，请先运行生产者或手动创建Topic", TOPIC);
                return;
            }
            log.info("Topic {} 存在", TOPIC);

            // 创建消费组（广播模式）
            boolean groupReady = resourceManager.ensureConsumerGroupExists(CONSUMER_GROUP, true);
            if (groupReady) {
                log.info("消费组 {} 创建/确认成功（广播模式）", CONSUMER_GROUP);
            } else {
                log.error("消费组 {} 创建失败，无法启动消费者", CONSUMER_GROUP);
                return;
            }

            resourceManager.printTopicInfo(TOPIC);
        } finally {
            resourceManager.shutdown();
        }

        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(CONSUMER_GROUP);
        consumer.setNamesrvAddr(NAME_SERVER);
        consumer.setInstanceName("BroadcastConsumer_" + System.currentTimeMillis());
        
        // 关键配置：设置为广播模式
        consumer.setMessageModel(MessageModel.BROADCASTING);
        
        // 设置从最新位置开始消费（避免消费历史消息）
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        
        // 订阅主题，接收所有标签的消息
        consumer.subscribe(TOPIC, "*");
        
        // 设置消费线程数
        consumer.setConsumeThreadMin(2);
        consumer.setConsumeThreadMax(4);
        
        AtomicLong consumedCount = new AtomicLong(0);
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong failureCount = new AtomicLong(0);
        
        // 注册消息监听器
        consumer.registerMessageListener((MessageListenerConcurrently) (messages, context) -> {
            try {
                messages.forEach(message -> {
                    long count = consumedCount.incrementAndGet();
                    String body = new String(message.getBody());
                    
                    // 获取消息属性
                    String messageId = message.getUserProperty("messageId");
                    String sendTime = message.getUserProperty("sendTime");
                    long receiveTime = System.currentTimeMillis();
                    long latency = sendTime != null ? receiveTime - Long.parseLong(sendTime) : 0;
                    
                    if (count % 100 == 0) { // 每100条消息打印一次详细日志
                        log.info("广播消费者收到消息 #{}: {}", count, body);
                        log.info("消息详情 - Topic: {}, Tag: {}, MsgId: {}, 延迟: {}ms", 
                                message.getTopic(), message.getTags(), message.getMsgId(), latency);
                    }
                    
                    successCount.incrementAndGet();
                });
                
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                failureCount.addAndGet(messages.size());
                log.error("消费消息失败", e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        });
        
        try {
            consumer.start();
            log.info("=== 广播模式消费者启动成功 ===");
            log.info("NameServer: {}", NAME_SERVER);
            log.info("Topic: {}", TOPIC);
            log.info("Consumer Group: {}", CONSUMER_GROUP);
            log.info("消息模式: 广播模式 (BROADCASTING)");
            log.info("消费位置: 从最新位置开始 (CONSUME_FROM_LAST_OFFSET)");
            log.info("消费线程数: 2-4");
            log.info("================================");
            
            // 定期打印消费统计
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.scheduleAtFixedRate(() -> {
                long total = consumedCount.get();
                long success = successCount.get();
                long failure = failureCount.get();
                
                log.info("=== 消费统计 ===");
                log.info("总消费: {} 条", total);
                log.info("成功: {} 条", success);
                log.info("失败: {} 条", failure);
                if (total > 0) {
                    log.info("成功率: {:.2f}%", success * 100.0 / total);
                    log.info("当前消费速率: {:.1f} 条/分钟", total / ((System.currentTimeMillis() - startTime) / 60000.0));
                }
                log.info("===============");
            }, 1, 1, TimeUnit.MINUTES);
            
            // 添加优雅关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("正在关闭消费者...");
                scheduler.shutdown();
                try {
                    if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                        scheduler.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    scheduler.shutdownNow();
                }
                consumer.shutdown();
                log.info("消费者已关闭，总共消费消息: {} 条", consumedCount.get());
            }));
            
            // 保持运行状态
            log.info("广播消费者正在运行，按 Ctrl+C 停止...");
            Thread.currentThread().join();
            
        } catch (MQClientException e) {
            log.error("消费者启动失败", e);
            throw e;
        }
    }
    
    private static final long startTime = System.currentTimeMillis();
}
