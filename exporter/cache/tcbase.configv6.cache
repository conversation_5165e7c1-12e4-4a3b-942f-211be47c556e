[{"projectName": "tcbase.tp.hd", "config": [], "version": 0}, {"projectName": "tcbase.tp.hd", "config": [{"key": "TCBase.Cache.v3", "value": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"yes\"?>\n<tcbase.cache>\n    <cache enabled=\"true\" name=\"tp_hd.redis.proxy\" needPrefixKey=\"false\" scene=\"S\" type=\"S\">\n        <redis enabled=\"true\" ip=\"rediscache2.cdb.17usoft.com:3611\" maxPool=\"20\" minPool=\"3\" password=\"tcbase.tp.hd:qa:<EMAIL>.v3:fbe17f49\" sentinel=\"false\" timeOut=\"3000\"/>\n    </cache>\n    <cache enabled=\"true\" name=\"tp_hd\" needPrefixKey=\"false\" scene=\"C\" type=\"C\">\n        <redis enabled=\"true\" ip=\"************:10625\" maxPool=\"20\" minPool=\"3\" password=\"\" sentinel=\"false\" timeOut=\"3000\"/>\n        <redis enabled=\"true\" ip=\"*************:10665\" maxPool=\"20\" minPool=\"3\" password=\"\" sentinel=\"false\" timeOut=\"3000\"/>\n        <redis enabled=\"true\" ip=\"*************:10815\" maxPool=\"20\" minPool=\"3\" password=\"\" sentinel=\"false\" timeOut=\"3000\"/>\n        <redis enabled=\"true\" ip=\"*************:10924\" maxPool=\"20\" minPool=\"3\" password=\"\" sentinel=\"false\" timeOut=\"3000\"/>\n        <redis enabled=\"true\" ip=\"*************:10969\" maxPool=\"20\" minPool=\"3\" password=\"\" sentinel=\"false\" timeOut=\"3000\"/>\n        <redis enabled=\"true\" ip=\"*************:10965\" maxPool=\"20\" minPool=\"3\" password=\"\" sentinel=\"false\" timeOut=\"3000\"/>\n    </cache>\n</tcbase.cache>\n", "env": "qa", "serverRoom": "<PERSON><PERSON><PERSON>", "version": "1845712146546053120"}, {"key": "TCBase.Cache.v2", "value": "[{\"instances\":[{\"ip\":\"************:10625\",\"password\":\"\",\"sentinel\":false},{\"ip\":\"*************:10665\",\"password\":\"\",\"sentinel\":false},{\"ip\":\"*************:10815\",\"password\":\"\",\"sentinel\":false},{\"ip\":\"*************:10924\",\"password\":\"\",\"sentinel\":false},{\"ip\":\"*************:10969\",\"password\":\"\",\"sentinel\":false},{\"ip\":\"*************:10965\",\"password\":\"\",\"sentinel\":false}],\"name\":\"tp_hd\",\"type\":\"C\"},{\"instances\":[{\"ip\":\"rediscache2.cdb.17usoft.com:3611\",\"password\":\"tcbase.tp.hd:qa:<EMAIL>.v3:fbe17f49\",\"sentinel\":false}],\"name\":\"tp_hd\",\"type\":\"S\"},{\"instances\":[{\"ip\":\"rediscache2.cdb.17usoft.com:3611\",\"password\":\"tcbase.tp.hd:qa:<EMAIL>.v3:fbe17f49\",\"sentinel\":false}],\"name\":\"tp_hd.redis.proxy\",\"type\":\"S\"}]", "env": "qa", "serverRoom": "<PERSON><PERSON><PERSON>", "version": "1845712146546053120"}, {"key": "TCBase.ConfigCenter.retention-inner-Key", "value": "TCBase.ConfigCenter.retention-inner-Value1831175967453102122", "env": "qa", "serverRoom": "<PERSON><PERSON><PERSON>", "version": "1845712146546053120"}, {"key": "TCBase.ConfigCenter.retention-Key", "value": "TCBase.ConfigCenter.retention-Value", "env": "qa", "serverRoom": "<PERSON><PERSON><PERSON>", "version": "1845712146546053120"}, {"key": "TCBase.Cache", "value": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"yes\"?>\n<tcbase.cache>\n    <cache enabled=\"true\" name=\"tp_hd\" needPrefixKey=\"false\" scene=\"S\" type=\"S\">\n        <redis enabled=\"true\" ip=\"rediscache2.cdb.17usoft.com:3611\" maxPool=\"20\" minPool=\"3\" password=\"tcbase.tp.hd:qa:<EMAIL>.v3:fbe17f49\" sentinel=\"false\" timeOut=\"3000\"/>\n    </cache>\n    <cache enabled=\"true\" name=\"tp_hd.redis.proxy\" needPrefixKey=\"false\" scene=\"S\" type=\"S\">\n        <redis enabled=\"true\" ip=\"rediscache2.cdb.17usoft.com:3611\" maxPool=\"20\" minPool=\"3\" password=\"tcbase.tp.hd:qa:<EMAIL>.v3:fbe17f49\" sentinel=\"false\" timeOut=\"3000\"/>\n    </cache>\n</tcbase.cache>\n", "env": "qa", "serverRoom": "<PERSON><PERSON><PERSON>", "version": "1845712146546053120"}], "version": 1845712146546053120, "env": "qa", "serverRoom": "officeidc_hd2"}, {"projectName": "oceanxinjian1.java.tcbase.cache", "config": [{"key": "TCBase.Cache.AllUsingSingle", "value": "false", "env": "qa", "serverRoom": "<PERSON><PERSON><PERSON>", "version": "1914524583860375552"}, {"key": "AllUsingSingle", "value": "true1", "env": "qa", "serverRoom": "<PERSON><PERSON><PERSON>", "version": "1914524583860375552"}, {"key": "TCBase.ConfigCenter.retention-inner-Key", "value": "TCBase.ConfigCenter.retention-inner-Value1831175967453101721", "env": "qa", "serverRoom": "<PERSON><PERSON><PERSON>", "version": "1914524583860375552"}], "version": 1914524583860375552, "env": "qa", "serverRoom": "officeidc_hd2"}]